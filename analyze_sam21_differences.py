#!/usr/bin/env python3
"""
Detailed analysis of SAM 2.0 vs SAM 2.1 differences for ONNX export compatibility
"""

import os
import torch
import yaml
from omegaconf import OmegaConf

def analyze_config_differences():
    """Compare SAM 2.0 and SAM 2.1 configuration files"""
    
    print("=== Configuration Differences Analysis ===\n")
    
    # Load SAM 2.0 config
    with open("sam2_configs/sam2_hiera_t.yaml", 'r') as f:
        sam20_config = yaml.safe_load(f)
    
    # Load SAM 2.1 config
    with open("sam2.1_configs/sam2.1_hiera_t.yaml", 'r') as f:
        sam21_config = yaml.safe_load(f)
    
    def extract_model_params(config, path="model"):
        """Extract all model parameters from config"""
        params = {}
        
        def traverse(obj, current_path):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    new_path = f"{current_path}.{key}" if current_path else key
                    if isinstance(value, (dict, list)):
                        traverse(value, new_path)
                    else:
                        params[new_path] = value
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    new_path = f"{current_path}[{i}]"
                    if isinstance(item, (dict, list)):
                        traverse(item, new_path)
                    else:
                        params[new_path] = item
        
        traverse(config.get("model", {}), "")
        return params
    
    sam20_params = extract_model_params(sam20_config)
    sam21_params = extract_model_params(sam21_config)
    
    sam20_keys = set(sam20_params.keys())
    sam21_keys = set(sam21_params.keys())
    
    only_in_sam20 = sam20_keys - sam21_keys
    only_in_sam21 = sam21_keys - sam20_keys
    common_keys = sam20_keys & sam21_keys
    
    print(f"SAM 2.0 parameters: {len(sam20_keys)}")
    print(f"SAM 2.1 parameters: {len(sam21_keys)}")
    print(f"Common parameters: {len(common_keys)}")
    print(f"Only in SAM 2.0: {len(only_in_sam20)}")
    print(f"Only in SAM 2.1: {len(only_in_sam21)}")
    print()
    
    if only_in_sam20:
        print("Parameters only in SAM 2.0:")
        for key in sorted(only_in_sam20):
            print(f"  - {key}: {sam20_params[key]}")
        print()
    
    if only_in_sam21:
        print("Parameters only in SAM 2.1:")
        for key in sorted(only_in_sam21):
            print(f"  - {key}: {sam21_params[key]}")
        print()
    
    # Check for value differences in common keys
    value_differences = []
    for key in common_keys:
        if sam20_params[key] != sam21_params[key]:
            value_differences.append((key, sam20_params[key], sam21_params[key]))
    
    if value_differences:
        print("Value differences in common parameters:")
        for key, sam20_val, sam21_val in value_differences:
            print(f"  - {key}: SAM 2.0 '{sam20_val}' vs SAM 2.1 '{sam21_val}'")
        print()
    
    return {
        "sam20_params": sam20_params,
        "sam21_params": sam21_params,
        "only_in_sam20": only_in_sam20,
        "only_in_sam21": only_in_sam21,
        "value_differences": value_differences
    }

def analyze_state_dict_differences():
    """Analyze differences between SAM 2.0 and SAM 2.1 state dictionaries"""
    
    print("=== State Dictionary Differences Analysis ===\n")
    
    sam20_checkpoint = "./checkpoints/sam2_hiera_tiny.pt"
    sam21_checkpoint = "./checkpoints/sam2.1_hiera_tiny.pt"
    
    if not os.path.exists(sam20_checkpoint):
        print(f"❌ SAM 2.0 checkpoint not found: {sam20_checkpoint}")
        return None
    
    if not os.path.exists(sam21_checkpoint):
        print(f"❌ SAM 2.1 checkpoint not found: {sam21_checkpoint}")
        return None
    
    try:
        # Load state dictionaries
        sam20_state = torch.load(sam20_checkpoint, map_location="cpu")
        sam21_state = torch.load(sam21_checkpoint, map_location="cpu")
        
        sam20_keys = set(sam20_state.keys())
        sam21_keys = set(sam21_state.keys())
        
        # Find differences
        only_in_sam20 = sam20_keys - sam21_keys
        only_in_sam21 = sam21_keys - sam20_keys
        common_keys = sam20_keys & sam21_keys
        
        print(f"SAM 2.0 state dict keys: {len(sam20_keys)}")
        print(f"SAM 2.1 state dict keys: {len(sam21_keys)}")
        print(f"Common keys: {len(common_keys)}")
        print(f"Only in SAM 2.0: {len(only_in_sam20)}")
        print(f"Only in SAM 2.1: {len(only_in_sam21)}")
        print()
        
        if only_in_sam20:
            print("Keys only in SAM 2.0:")
            for key in sorted(only_in_sam20):
                tensor = sam20_state[key]
                shape_info = f" (shape: {tensor.shape})" if hasattr(tensor, 'shape') else ""
                print(f"  - {key}{shape_info}")
            print()
        
        if only_in_sam21:
            print("Keys only in SAM 2.1:")
            for key in sorted(only_in_sam21):
                tensor = sam21_state[key]
                shape_info = f" (shape: {tensor.shape})" if hasattr(tensor, 'shape') else ""
                print(f"  - {key}{shape_info}")
            print()
        
        # Check for shape differences in common keys
        shape_differences = []
        for key in common_keys:
            if hasattr(sam20_state[key], 'shape') and hasattr(sam21_state[key], 'shape'):
                if sam20_state[key].shape != sam21_state[key].shape:
                    shape_differences.append((key, sam20_state[key].shape, sam21_state[key].shape))
        
        if shape_differences:
            print("Shape differences in common keys:")
            for key, sam20_shape, sam21_shape in shape_differences:
                print(f"  - {key}: SAM 2.0 {sam20_shape} vs SAM 2.1 {sam21_shape}")
            print()
        
        return {
            "sam20_keys": sam20_keys,
            "sam21_keys": sam21_keys,
            "only_in_sam20": only_in_sam20,
            "only_in_sam21": only_in_sam21,
            "common_keys": common_keys,
            "shape_differences": shape_differences
        }
        
    except Exception as e:
        print(f"❌ Failed to analyze state dictionaries: {str(e)}")
        return None

def analyze_onnx_export_compatibility():
    """Analyze what changes are needed for ONNX export compatibility"""
    
    print("=== ONNX Export Compatibility Analysis ===\n")
    
    # Key findings from our tests
    print("Key Compatibility Issues Found:")
    print()
    
    print("1. Configuration Loading:")
    print("   - SAM 2.1 configs are in sam2.1_configs/ directory")
    print("   - Hydra is configured to only search sam2_configs/ directory")
    print("   - Need to modify Hydra initialization or copy configs")
    print()
    
    print("2. State Dictionary Differences:")
    print("   - SAM 2.1 checkpoints contain new keys:")
    print("     * no_obj_embed_spatial")
    print("     * obj_ptr_tpos_proj.weight")
    print("     * obj_ptr_tpos_proj.bias")
    print("   - These correspond to new SAM 2.1 features")
    print()
    
    print("3. Configuration Parameter Differences:")
    print("   - SAM 2.1 configs have additional parameters:")
    print("     * no_obj_embed_spatial: true")
    print("     * add_tpos_enc_to_obj_ptrs: true")
    print("     * proj_tpos_enc_in_obj_ptrs: true")
    print("     * use_signed_tpos_enc_to_obj_ptrs: true")
    print("   - Different feat_sizes in RoPEAttention (32x32 vs 64x64)")
    print()
    
    print("4. Model Architecture Changes:")
    print("   - New temporal positional encoding projection layer")
    print("   - Enhanced object pointer handling")
    print("   - Spatial embedding for no-object cases")
    print()

def generate_compatibility_recommendations():
    """Generate recommendations for SAM 2.1 compatibility"""
    
    print("=== Compatibility Recommendations ===\n")
    
    print("OPTION 1: Minimal Changes for SAM 2.1 Support")
    print("=" * 50)
    print()
    print("1. Configuration Setup:")
    print("   - Copy sam2.1_configs to sam2_configs or modify Hydra setup")
    print("   - Update build_sam.py to handle both config directories")
    print()
    print("2. Model Code Updates:")
    print("   - Ensure SAM2Base supports new SAM 2.1 parameters")
    print("   - Add conditional logic for new features")
    print("   - Handle missing state dict keys gracefully")
    print()
    print("3. ONNX Export Updates:")
    print("   - Test ONNX export with SAM 2.1 models")
    print("   - Verify new layers export correctly")
    print("   - Update export scripts to handle new parameters")
    print()
    
    print("OPTION 2: Hybrid Approach")
    print("=" * 30)
    print()
    print("1. Use SAM 2.0 architecture with SAM 2.1 checkpoints:")
    print("   - Strip incompatible keys from SAM 2.1 checkpoints")
    print("   - Use SAM 2.0 configs with modified checkpoints")
    print("   - May lose some SAM 2.1 improvements")
    print()
    
    print("OPTION 3: Continue with SAM 2.0")
    print("=" * 35)
    print()
    print("1. Stick with SAM 2.0 for ONNX export:")
    print("   - Current setup works well with SAM 2.0")
    print("   - No compatibility issues")
    print("   - Proven ONNX export functionality")
    print()
    
    print("RECOMMENDATION:")
    print("For the axinc-ai ONNX export fork, OPTION 3 (continue with SAM 2.0)")
    print("is recommended because:")
    print("- ONNX export is the primary goal")
    print("- SAM 2.0 export is already working")
    print("- SAM 2.1 improvements may not be critical for ONNX deployment")
    print("- Minimizes risk of breaking existing functionality")

if __name__ == "__main__":
    print("SAM 2.0 vs SAM 2.1 Detailed Analysis\n")
    
    # Analyze configuration differences
    config_analysis = analyze_config_differences()
    
    # Analyze state dictionary differences
    state_dict_analysis = analyze_state_dict_differences()
    
    # Analyze ONNX export compatibility
    analyze_onnx_export_compatibility()
    
    # Generate recommendations
    generate_compatibility_recommendations()
