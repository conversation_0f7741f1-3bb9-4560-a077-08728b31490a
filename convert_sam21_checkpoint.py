#!/usr/bin/env python3
"""
Convert SAM 2.1 checkpoints to SAM 2.0 compatible format
This demonstrates the hybrid approach for SAM 2.1 compatibility
"""

import os
import torch
import arg<PERSON>se

def convert_sam21_to_sam20_checkpoint(sam21_path, sam20_path, verbose=True):
    """
    Convert SAM 2.1 checkpoint to SAM 2.0 compatible format by removing incompatible keys
    
    Args:
        sam21_path: Path to SAM 2.1 checkpoint
        sam20_path: Path to save converted SAM 2.0 compatible checkpoint
        verbose: Whether to print detailed information
    """
    
    if not os.path.exists(sam21_path):
        raise FileNotFoundError(f"SAM 2.1 checkpoint not found: {sam21_path}")
    
    if verbose:
        print(f"Loading SAM 2.1 checkpoint: {sam21_path}")
    
    # Load SAM 2.1 checkpoint
    checkpoint = torch.load(sam21_path, map_location="cpu")
    
    if verbose:
        print(f"Checkpoint type: {type(checkpoint)}")
        if isinstance(checkpoint, dict):
            print(f"Top-level keys: {list(checkpoint.keys())}")
    
    # Extract model state dict
    if isinstance(checkpoint, dict) and 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint
        checkpoint = {'model': state_dict}
    
    if verbose:
        print(f"Original state dict keys: {len(state_dict)}")
    
    # SAM 2.1 specific keys that need to be removed for SAM 2.0 compatibility
    sam21_specific_keys = [
        "no_obj_embed_spatial",
        "obj_ptr_tpos_proj.weight", 
        "obj_ptr_tpos_proj.bias"
    ]
    
    removed_keys = []
    for key in sam21_specific_keys:
        if key in state_dict:
            if verbose:
                tensor = state_dict[key]
                shape_info = f" (shape: {tensor.shape})" if hasattr(tensor, 'shape') else ""
                print(f"Removing SAM 2.1 key: {key}{shape_info}")
            del state_dict[key]
            removed_keys.append(key)
    
    if verbose:
        print(f"Removed {len(removed_keys)} SAM 2.1 specific keys")
        print(f"Converted state dict keys: {len(state_dict)}")
    
    # Save converted checkpoint
    if verbose:
        print(f"Saving converted checkpoint: {sam20_path}")
    
    torch.save(checkpoint, sam20_path)
    
    if verbose:
        print("✅ Conversion completed successfully")
        print(f"Removed keys: {removed_keys}")
    
    return removed_keys

def test_converted_checkpoint(converted_path, verbose=True):
    """
    Test loading the converted checkpoint with SAM 2.0 config
    """
    
    if verbose:
        print(f"\n=== Testing Converted Checkpoint ===")
        print(f"Loading: {converted_path}")
    
    try:
        from sam2.build_sam import build_sam2
        
        # Try to load with SAM 2.0 config
        model = build_sam2(
            config_file="sam2_hiera_t.yaml",
            ckpt_path=converted_path,
            device="cpu",
            image_size=1024
        )
        
        if verbose:
            print("✅ Successfully loaded converted checkpoint with SAM 2.0 config")
            print(f"Model type: {type(model)}")
        
        return True
        
    except Exception as e:
        if verbose:
            print(f"❌ Failed to load converted checkpoint: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Convert SAM 2.1 checkpoint to SAM 2.0 compatible format")
    parser.add_argument("--input", required=True, help="Path to SAM 2.1 checkpoint")
    parser.add_argument("--output", required=True, help="Path to save converted checkpoint")
    parser.add_argument("--test", action="store_true", help="Test the converted checkpoint")
    parser.add_argument("--quiet", action="store_true", help="Suppress verbose output")
    
    args = parser.parse_args()
    
    verbose = not args.quiet
    
    try:
        # Convert checkpoint
        removed_keys = convert_sam21_to_sam20_checkpoint(
            args.input, 
            args.output, 
            verbose=verbose
        )
        
        # Test if requested
        if args.test:
            success = test_converted_checkpoint(args.output, verbose=verbose)
            if not success:
                print("⚠️  Converted checkpoint failed to load - may need additional modifications")
        
        if verbose:
            print(f"\n=== Summary ===")
            print(f"Input: {args.input}")
            print(f"Output: {args.output}")
            print(f"Removed {len(removed_keys)} SAM 2.1 specific keys")
            print("Conversion completed successfully!")
        
    except Exception as e:
        print(f"❌ Conversion failed: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    # Example usage if run directly
    if len(os.sys.argv) == 1:
        print("SAM 2.1 to SAM 2.0 Checkpoint Converter")
        print("\nExample usage:")
        print("python convert_sam21_checkpoint.py --input ./checkpoints/sam2.1_hiera_tiny.pt --output ./checkpoints/sam2.1_hiera_tiny_converted.pt --test")
        print("\nThis will:")
        print("1. Remove SAM 2.1 specific keys from the checkpoint")
        print("2. Save a SAM 2.0 compatible version")
        print("3. Test loading with SAM 2.0 config")
        print("\nNote: This approach loses SAM 2.1 improvements but maintains compatibility")
    else:
        exit(main())
