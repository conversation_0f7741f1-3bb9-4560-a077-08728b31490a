#!/usr/bin/env python3
"""
Detailed state dictionary analysis for SAM 2.0 vs SAM 2.1
"""

import os
import torch

def analyze_checkpoint_structure():
    """Analyze the structure of SAM checkpoints"""
    
    print("=== Checkpoint Structure Analysis ===\n")
    
    sam20_checkpoint = "./checkpoints/sam2_hiera_tiny.pt"
    sam21_checkpoint = "./checkpoints/sam2.1_hiera_tiny.pt"
    
    for name, path in [("SAM 2.0", sam20_checkpoint), ("SAM 2.1", sam21_checkpoint)]:
        if not os.path.exists(path):
            print(f"❌ {name} checkpoint not found: {path}")
            continue
            
        print(f"{name} Checkpoint: {path}")
        try:
            checkpoint = torch.load(path, map_location="cpu")
            print(f"  Type: {type(checkpoint)}")
            
            if isinstance(checkpoint, dict):
                print(f"  Top-level keys: {list(checkpoint.keys())}")
                
                # Check if it's a nested structure
                for key, value in checkpoint.items():
                    print(f"    {key}: {type(value)}")
                    if isinstance(value, dict):
                        print(f"      Sub-keys: {len(value)} items")
                        # Show first few keys
                        sub_keys = list(value.keys())[:10]
                        print(f"      Sample keys: {sub_keys}")
                        if len(value) > 10:
                            print(f"      ... and {len(value) - 10} more")
                    elif hasattr(value, 'shape'):
                        print(f"      Shape: {value.shape}")
            else:
                print(f"  Direct model state dict with {len(checkpoint)} keys")
                
        except Exception as e:
            print(f"  ❌ Error loading: {e}")
        
        print()

def compare_model_state_dicts():
    """Compare the actual model state dictionaries"""
    
    print("=== Model State Dictionary Comparison ===\n")
    
    sam20_checkpoint = "./checkpoints/sam2_hiera_tiny.pt"
    sam21_checkpoint = "./checkpoints/sam2.1_hiera_tiny.pt"
    
    if not os.path.exists(sam20_checkpoint):
        print(f"❌ SAM 2.0 checkpoint not found: {sam20_checkpoint}")
        return None
    
    if not os.path.exists(sam21_checkpoint):
        print(f"❌ SAM 2.1 checkpoint not found: {sam21_checkpoint}")
        return None
    
    try:
        # Load checkpoints
        sam20_checkpoint_data = torch.load(sam20_checkpoint, map_location="cpu")
        sam21_checkpoint_data = torch.load(sam21_checkpoint, map_location="cpu")
        
        # Extract state dictionaries
        if isinstance(sam20_checkpoint_data, dict) and 'model' in sam20_checkpoint_data:
            sam20_state = sam20_checkpoint_data['model']
        else:
            sam20_state = sam20_checkpoint_data
            
        if isinstance(sam21_checkpoint_data, dict) and 'model' in sam21_checkpoint_data:
            sam21_state = sam21_checkpoint_data['model']
        else:
            sam21_state = sam21_checkpoint_data
        
        print(f"SAM 2.0 state dict type: {type(sam20_state)}")
        print(f"SAM 2.1 state dict type: {type(sam21_state)}")
        
        if not isinstance(sam20_state, dict) or not isinstance(sam21_state, dict):
            print("❌ State dictionaries are not in expected format")
            return None
        
        sam20_keys = set(sam20_state.keys())
        sam21_keys = set(sam21_state.keys())
        
        print(f"SAM 2.0 state dict keys: {len(sam20_keys)}")
        print(f"SAM 2.1 state dict keys: {len(sam21_keys)}")
        
        # Find differences
        only_in_sam20 = sam20_keys - sam21_keys
        only_in_sam21 = sam21_keys - sam20_keys
        common_keys = sam20_keys & sam21_keys
        
        print(f"Common keys: {len(common_keys)}")
        print(f"Only in SAM 2.0: {len(only_in_sam20)}")
        print(f"Only in SAM 2.1: {len(only_in_sam21)}")
        print()
        
        if only_in_sam20:
            print("Keys only in SAM 2.0:")
            for key in sorted(only_in_sam20):
                tensor = sam20_state[key]
                shape_info = f" (shape: {tensor.shape})" if hasattr(tensor, 'shape') else f" (type: {type(tensor)})"
                print(f"  - {key}{shape_info}")
            print()
        
        if only_in_sam21:
            print("Keys only in SAM 2.1:")
            for key in sorted(only_in_sam21):
                tensor = sam21_state[key]
                shape_info = f" (shape: {tensor.shape})" if hasattr(tensor, 'shape') else f" (type: {type(tensor)})"
                print(f"  - {key}{shape_info}")
            print()
        
        # Check for shape differences in common keys
        shape_differences = []
        for key in common_keys:
            if hasattr(sam20_state[key], 'shape') and hasattr(sam21_state[key], 'shape'):
                if sam20_state[key].shape != sam21_state[key].shape:
                    shape_differences.append((key, sam20_state[key].shape, sam21_state[key].shape))
        
        if shape_differences:
            print("Shape differences in common keys:")
            for key, sam20_shape, sam21_shape in shape_differences:
                print(f"  - {key}: SAM 2.0 {sam20_shape} vs SAM 2.1 {sam21_shape}")
            print()
        else:
            print("No shape differences found in common keys.")
            print()
        
        # Show some sample keys for context
        print("Sample keys from both models:")
        sample_keys = sorted(list(common_keys))[:20]
        for key in sample_keys:
            sam20_shape = sam20_state[key].shape if hasattr(sam20_state[key], 'shape') else 'N/A'
            sam21_shape = sam21_state[key].shape if hasattr(sam21_state[key], 'shape') else 'N/A'
            print(f"  {key}: {sam20_shape}")
        
        if len(common_keys) > 20:
            print(f"  ... and {len(common_keys) - 20} more common keys")
        
        return {
            "sam20_keys": sam20_keys,
            "sam21_keys": sam21_keys,
            "only_in_sam20": only_in_sam20,
            "only_in_sam21": only_in_sam21,
            "common_keys": common_keys,
            "shape_differences": shape_differences
        }
        
    except Exception as e:
        print(f"❌ Failed to analyze state dictionaries: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def analyze_specific_sam21_keys():
    """Look specifically for the SAM 2.1 keys we expect"""
    
    print("=== SAM 2.1 Specific Keys Analysis ===\n")
    
    sam21_checkpoint = "./checkpoints/sam2.1_hiera_tiny.pt"
    
    if not os.path.exists(sam21_checkpoint):
        print(f"❌ SAM 2.1 checkpoint not found: {sam21_checkpoint}")
        return
    
    try:
        checkpoint_data = torch.load(sam21_checkpoint, map_location="cpu")
        
        # Extract state dictionary
        if isinstance(checkpoint_data, dict) and 'model' in checkpoint_data:
            state_dict = checkpoint_data['model']
        else:
            state_dict = checkpoint_data
        
        expected_sam21_keys = [
            "no_obj_embed_spatial",
            "obj_ptr_tpos_proj.weight", 
            "obj_ptr_tpos_proj.bias"
        ]
        
        print("Looking for expected SAM 2.1 specific keys:")
        for key in expected_sam21_keys:
            if key in state_dict:
                tensor = state_dict[key]
                shape_info = f" (shape: {tensor.shape})" if hasattr(tensor, 'shape') else f" (type: {type(tensor)})"
                print(f"  ✅ {key}{shape_info}")
            else:
                print(f"  ❌ {key} - NOT FOUND")
        
        print()
        
        # Look for keys containing these patterns
        print("Keys containing 'obj_ptr':")
        obj_ptr_keys = [k for k in state_dict.keys() if 'obj_ptr' in k]
        for key in obj_ptr_keys:
            tensor = state_dict[key]
            shape_info = f" (shape: {tensor.shape})" if hasattr(tensor, 'shape') else f" (type: {type(tensor)})"
            print(f"  - {key}{shape_info}")
        
        print()
        
        print("Keys containing 'no_obj':")
        no_obj_keys = [k for k in state_dict.keys() if 'no_obj' in k]
        for key in no_obj_keys:
            tensor = state_dict[key]
            shape_info = f" (shape: {tensor.shape})" if hasattr(tensor, 'shape') else f" (type: {type(tensor)})"
            print(f"  - {key}{shape_info}")
        
    except Exception as e:
        print(f"❌ Failed to analyze SAM 2.1 keys: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Detailed SAM 2.0 vs SAM 2.1 State Dictionary Analysis\n")
    
    # Analyze checkpoint structure
    analyze_checkpoint_structure()
    
    # Compare model state dictionaries
    comparison_result = compare_model_state_dicts()
    
    # Look for specific SAM 2.1 keys
    analyze_specific_sam21_keys()
