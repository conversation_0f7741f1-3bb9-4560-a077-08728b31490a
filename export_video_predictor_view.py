# Export memory attention and memory encoder
# Implemented by ax Inc. 2024

import argparse
parser = argparse.ArgumentParser()
parser.add_argument('--model_id', default="hiera_t", choices=["hiera_l", "hiera_b+", "hiera_s", "hiera_t"])
parser.add_argument('--framework', default="onnx", choices=["onnx", "tflite", "torch"])
parser.add_argument('--accuracy', default="float", choices=["float", "int8"])
parser.add_argument('--mode', default="both", choices=["both", "import", "export"])
parser.add_argument('--image_size', default=1024, type=int, choices=[512, 1024])
parser.add_argument('--video_dir', type=str, help='Path to the video directory containing frame images')
args = parser.parse_args()

import os
import numpy as np
import torch
import matplotlib.pyplot as plt
from matplotlib.widgets import Button
from PIL import Image
from pathlib import Path

# output
os.makedirs("output", exist_ok=True)
os.makedirs("model", exist_ok=True)

# export settings
model_id = args.model_id

export_to_onnx = args.framework=="onnx" and (args.mode=="export" or args.mode=="both")
import_from_onnx = args.framework=="onnx" and (args.mode=="import" or args.mode=="both")
export_to_tflite = args.framework=="tflite" and (args.mode=="export" or args.mode=="both")
import_from_tflite = args.framework=="tflite" and (args.mode=="import" or args.mode=="both")

# import
if model_id == "hiera_l":
    model_cfg = "sam2_hiera_l.yaml"
    sam2_checkpoint = "./checkpoints/sam2_hiera_large.pt"
elif model_id == "hiera_s":
    model_cfg = "sam2_hiera_s.yaml"
    sam2_checkpoint = "./checkpoints/sam2_hiera_small.pt"
elif model_id == "hiera_b+":
    model_cfg = "sam2_hiera_b+.yaml"
    sam2_checkpoint = "./checkpoints/sam2_hiera_base_plus.pt"
elif model_id == "hiera_t":
    model_cfg = "sam2_hiera_t.yaml"
    sam2_checkpoint = "./checkpoints/sam2_hiera_tiny.pt"
else:
    raise("unknown model type")

# resolution settings
if args.image_size == 512:
    model_id = model_id + "_512"

device = torch.device("cpu")
print(f"using device: {device}")

from sam2.build_sam import build_sam2_video_predictor

predictor = build_sam2_video_predictor(model_cfg, sam2_checkpoint, device=device, image_size=args.image_size)

if export_to_tflite or import_from_tflite:
    predictor.set_num_maskmem(num_maskmem=1, max_obj_ptrs_in_encoder=1)

def show_mask(mask, ax, obj_id=None, random_color=False):
    if random_color:
        color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
    else:
        cmap = plt.get_cmap("tab10")
        cmap_idx = 0 if obj_id is None else obj_id
        color = np.array([*cmap(cmap_idx)[:3], 0.6])
    h, w = mask.shape[-2:]
    mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
    ax.imshow(mask_image)


def show_points(coords, labels, ax, marker_size=200):
    pos_points = coords[labels==1]
    neg_points = coords[labels==0]
    ax.scatter(pos_points[:, 0], pos_points[:, 1], color='green', marker='*', s=marker_size, edgecolor='white', linewidth=1.25)
    ax.scatter(neg_points[:, 0], neg_points[:, 1], color='red', marker='*', s=marker_size, edgecolor='white', linewidth=1.25)


def show_box(box, ax):
    x0, y0 = box[0], box[1]
    w, h = box[2] - box[0], box[3] - box[1]
    ax.add_patch(plt.Rectangle((x0, y0), w, h, edgecolor='green', facecolor=(0, 0, 0, 0), lw=2))


class InteractivePointSelector:
    """Interactive point selection interface for SAM2 video frames."""

    def __init__(self):
        self.fig = None
        self.ax = None
        self.frame_image = None
        self.frame_width = 0
        self.frame_height = 0

        # Annotation storage
        self.points = []
        self.labels = []
        self.boxes = []

        # Visual elements
        self.point_markers = []
        self.box_patches = []

        # Interaction state
        self.annotation_mode = 'click'  # 'click' or 'box'
        self.box_start = None
        self.current_box_patch = None
        self.is_drawing_box = False
        self.finished = False

        # UI elements
        self.buttons = {}

    def setup_interface(self, frame_path):
        """Setup the interactive point selection interface."""
        # Load frame
        self.frame_image = Image.open(frame_path)
        frame_array = np.array(self.frame_image)
        self.frame_width, self.frame_height = self.frame_image.size

        # Create figure and axis
        plt.close('all')  # Close any existing figures
        plt.ion()  # Turn on interactive mode

        self.fig, self.ax = plt.subplots(1, 1, figsize=(14, 10))
        self.fig.suptitle(f"Interactive Point Selection - {Path(frame_path).name}", fontsize=14)

        # Display frame with proper extent to match image coordinates
        self.ax.imshow(frame_array, extent=[0, self.frame_width, self.frame_height, 0])
        self.ax.set_title(f"Frame Size: {self.frame_width}x{self.frame_height} | Mode: Click")
        self.ax.set_xlabel("X coordinate")
        self.ax.set_ylabel("Y coordinate")

        # Set axis limits to match image coordinates
        self.ax.set_xlim(0, self.frame_width)
        self.ax.set_ylim(self.frame_height, 0)  # Invert Y axis to match image coordinates

        # Setup event handlers
        self.cid_press = self.fig.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.cid_release = self.fig.canvas.mpl_connect('button_release_event', self.on_mouse_release)
        self.cid_motion = self.fig.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)

        # Setup UI buttons
        self.setup_buttons()

        # Show instructions
        self.show_instructions()

        plt.tight_layout()
        plt.show(block=False)
        plt.draw()

    def setup_buttons(self):
        """Setup UI buttons for annotation control."""
        # Button positions (left, bottom, width, height)
        button_height = 0.04
        button_width = 0.12
        button_spacing = 0.02
        start_x = 0.02
        start_y = 0.02

        # Click mode button
        ax_click = plt.axes([start_x, start_y, button_width, button_height])
        self.buttons['click'] = Button(ax_click, 'Click Mode')
        self.buttons['click'].on_clicked(lambda x: self.set_annotation_mode('click'))

        # Box mode button
        ax_box = plt.axes([start_x + button_width + button_spacing, start_y, button_width, button_height])
        self.buttons['box'] = Button(ax_box, 'Box Mode')
        self.buttons['box'].on_clicked(lambda x: self.set_annotation_mode('box'))

        # Clear button
        ax_clear = plt.axes([start_x + 2*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['clear'] = Button(ax_clear, 'Clear All')
        self.buttons['clear'].on_clicked(lambda x: self.clear_annotations())

        # Undo button
        ax_undo = plt.axes([start_x + 3*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['undo'] = Button(ax_undo, 'Undo Last')
        self.buttons['undo'].on_clicked(lambda x: self.undo_last_annotation())

        # Apply button
        ax_apply = plt.axes([start_x + 4*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['apply'] = Button(ax_apply, 'Apply & Close')
        self.buttons['apply'].on_clicked(lambda x: self.apply_annotations())

        # Update button colors
        self.update_button_colors()

    def update_button_colors(self):
        """Update button colors based on current annotation mode."""
        if 'click' in self.buttons:
            self.buttons['click'].color = 'lightgreen' if self.annotation_mode == 'click' else 'lightgray'
        if 'box' in self.buttons:
            self.buttons['box'].color = 'lightgreen' if self.annotation_mode == 'box' else 'lightgray'

        if self.fig:
            self.fig.canvas.draw()

    def set_annotation_mode(self, mode):
        """Set the annotation mode (click or box)."""
        self.annotation_mode = mode
        self.update_button_colors()

        if mode == 'click':
            self.ax.set_title(f"Click Mode - Left: Positive, Right: Negative | Frame: {self.frame_width}x{self.frame_height}")
        else:
            self.ax.set_title(f"Box Mode - Click and drag to draw bounding box | Frame: {self.frame_width}x{self.frame_height}")

        self.fig.canvas.draw()

    def show_instructions(self):
        """Show usage instructions."""
        print("=" * 60)
        print("Interactive Point Selection Interface")
        print("=" * 60)
        print("Instructions:")
        print("• Click Mode: Left click = positive point, Right click = negative point")
        print("• Box Mode: Click and drag to draw bounding boxes")
        print("• Use buttons to switch modes, clear, undo, or apply")
        print("• Click 'Apply & Close' when finished selecting points")
        print("=" * 60)
        print("Interface is ready for interaction...")

    def on_mouse_press(self, event):
        """Handle mouse press events."""
        if event.inaxes != self.ax:
            return

        x, y = event.xdata, event.ydata
        if x is None or y is None:
            return

        # Round coordinates to integers
        x, y = round(x), round(y)

        # Check bounds
        if x < 0 or x >= self.frame_width or y < 0 or y >= self.frame_height:
            print(f"Coordinates out of bounds: ({x}, {y})")
            return

        if self.annotation_mode == 'click':
            self.handle_click_annotation(x, y, event.button)
        elif self.annotation_mode == 'box':
            self.handle_box_start(x, y, event.button)

    def on_mouse_release(self, event):
        """Handle mouse release events."""
        if event.inaxes != self.ax or self.annotation_mode != 'box':
            return

        x, y = event.xdata, event.ydata
        if x is None or y is None:
            return

        x, y = round(x), round(y)
        self.handle_box_end(x, y, event.button)

    def on_mouse_move(self, event):
        """Handle mouse move events for box drawing."""
        if (event.inaxes != self.ax or self.annotation_mode != 'box' or
            not self.is_drawing_box or self.box_start is None):
            return

        x, y = event.xdata, event.ydata
        if x is None or y is None:
            return

        x, y = round(x), round(y)
        self.update_box_preview(x, y)

    def handle_click_annotation(self, x, y, button):
        """Handle click annotation (positive/negative points)."""
        # Left click = positive (1), Right click = negative (0)
        label = 1 if button == 1 else 0
        label_text = "positive" if label == 1 else "negative"

        print(f"Adding {label_text} point at ({x}, {y})")

        # Store the point and label
        self.points.append([x, y])
        self.labels.append(label)

        # Choose color and marker based on label
        if label == 1:  # Positive point
            color = 'green'
            marker = 'o'
            marker_size = 100
        else:  # Negative point
            color = 'red'
            marker = 'x'
            marker_size = 150

        # Add point marker
        point_marker = self.ax.scatter(
            x, y, c=color, marker=marker, s=marker_size,
            edgecolors='white', linewidths=2, zorder=10
        )
        self.point_markers.append(point_marker)

        # Update display
        self.fig.canvas.draw_idle()
        print(f"Total points: {len(self.points)}")

    def handle_box_start(self, x, y, button):
        """Handle start of bounding box drawing."""
        if button != 1:  # Only left click for boxes
            return

        self.box_start = (x, y)
        self.is_drawing_box = True

        # Create preview rectangle
        self.current_box_patch = plt.Rectangle(
            (x, y), 0, 0, linewidth=2, edgecolor='blue',
            facecolor='none', linestyle='--', alpha=0.7
        )
        self.ax.add_patch(self.current_box_patch)
        self.fig.canvas.draw_idle()

    def handle_box_end(self, x, y, button):
        """Handle end of bounding box drawing."""
        if not self.is_drawing_box or self.box_start is None or button != 1:
            return

        x1, y1 = self.box_start
        x2, y2 = x, y

        # Ensure proper box coordinates (x1,y1 = top-left, x2,y2 = bottom-right)
        box = [min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)]

        # Check if box has valid size
        if abs(x2 - x1) < 5 or abs(y2 - y1) < 5:
            print("Box too small, ignoring")
        else:
            print(f"Adding bounding box: {box}")
            self.boxes.append(box)

            # Remove preview and add final box
            if self.current_box_patch:
                self.current_box_patch.remove()

            # Create final rectangle
            final_rect = plt.Rectangle(
                (box[0], box[1]), box[2] - box[0], box[3] - box[1],
                linewidth=2, edgecolor='blue', facecolor='none', alpha=0.8
            )
            self.ax.add_patch(final_rect)
            self.box_patches.append(final_rect)

        # Reset drawing state
        self.box_start = None
        self.is_drawing_box = False
        self.current_box_patch = None

        self.fig.canvas.draw_idle()
        print(f"Total boxes: {len(self.boxes)}")

    def update_box_preview(self, x, y):
        """Update the preview rectangle while drawing."""
        if self.current_box_patch and self.box_start:
            x1, y1 = self.box_start
            width = x - x1
            height = y - y1

            self.current_box_patch.set_width(width)
            self.current_box_patch.set_height(height)
            self.fig.canvas.draw_idle()

    def clear_annotations(self):
        """Clear all annotations."""
        print("Clearing all annotations")

        # Clear stored data
        self.points.clear()
        self.labels.clear()
        self.boxes.clear()

        # Remove visual elements
        for marker in self.point_markers:
            marker.remove()
        self.point_markers.clear()

        for patch in self.box_patches:
            patch.remove()
        self.box_patches.clear()

        # Remove current box preview if exists
        if self.current_box_patch:
            self.current_box_patch.remove()
            self.current_box_patch = None

        # Reset drawing state
        self.box_start = None
        self.is_drawing_box = False

        self.fig.canvas.draw_idle()

    def undo_last_annotation(self):
        """Undo the last annotation."""
        if self.points:
            # Remove last point
            self.points.pop()
            self.labels.pop()

            # Remove visual marker
            if self.point_markers:
                marker = self.point_markers.pop()
                marker.remove()

            print(f"Removed last point. Remaining: {len(self.points)}")
        elif self.boxes:
            # Remove last box
            self.boxes.pop()

            # Remove visual patch
            if self.box_patches:
                patch = self.box_patches.pop()
                patch.remove()

            print(f"Removed last box. Remaining: {len(self.boxes)}")
        else:
            print("No annotations to undo")

        self.fig.canvas.draw_idle()

    def apply_annotations(self):
        """Apply annotations and close the interface."""
        print(f"Applying annotations: {len(self.points)} points, {len(self.boxes)} boxes")
        self.finished = True
        plt.close(self.fig)

    def get_annotations(self):
        """Get the collected annotations."""
        return {
            'points': np.array(self.points, dtype=np.float32) if self.points else np.array([], dtype=np.float32).reshape(0, 2),
            'labels': np.array(self.labels, dtype=np.int32) if self.labels else np.array([], dtype=np.int32),
            'boxes': self.boxes
        }

    def run(self):
        """Run the interactive interface and return annotations."""
        print("Starting interactive point selection...")

        # Keep the interface open until user finishes
        while not self.finished and plt.get_fignums():
            plt.pause(0.1)

        return self.get_annotations()


def get_interactive_annotations(frame_path):
    """Get annotations interactively from user."""
    selector = InteractivePointSelector()
    selector.setup_interface(frame_path)
    annotations = selector.run()

    print(f"Interactive selection complete:")
    print(f"  Points: {len(annotations['points'])}")
    print(f"  Boxes: {len(annotations['boxes'])}")

    return annotations

# Use command-line argument for video directory, with fallback to default
if args.video_dir:
    video_dir = args.video_dir
else:
    # Default fallback directories
    video_dir = "./notebooks/videos/bike-packing"
    print(f"No --video_dir specified, using default: {video_dir}")
# scan all the JPEG frame names in this directory
frame_names = [
    p for p in os.listdir(video_dir)
    if os.path.splitext(p)[-1] in [".jpg", ".jpeg", ".JPG", ".JPEG"]
]
frame_names.sort(key=lambda p: int(os.path.splitext(p)[0]))

inference_state = predictor.init_state(video_path=video_dir, import_from_onnx=import_from_onnx, import_from_tflite=import_from_tflite, model_id=model_id)
predictor.reset_state(inference_state)

ann_frame_idx = 0  # the frame index we interact with
ann_obj_id = 1  # give a unique id to each object we interact with (it can be any integers)

# Get interactive annotations from user
print(f"Opening interactive interface for frame {ann_frame_idx}...")
first_frame_path = os.path.join(video_dir, frame_names[ann_frame_idx])
annotations = get_interactive_annotations(first_frame_path)

# Extract points and labels from interactive selection
points = annotations['points']
labels = annotations['labels']
boxes = annotations['boxes']

print(f"User selected {len(points)} points and {len(boxes)} boxes")

# Validate that we have at least some annotations
if len(points) == 0 and len(boxes) == 0:
    print("No annotations provided. Using default points for demonstration.")
    # Fallback to default points if no interactive selection
    if args.framework == "tflite":
        points = np.array([[388, 165]], dtype=np.float32)
        labels = np.array([1], np.int32)
    else:
        points = np.array([[388, 165], [400, 179]], dtype=np.float32)
        labels = np.array([1, 1], np.int32)
    boxes = []

# Add annotations to the predictor
out_obj_ids = []
out_mask_logits = []

# Process points if available
if len(points) > 0:
    print(f"Adding {len(points)} points to predictor...")
    _, obj_ids, mask_logits = predictor.add_new_points_or_box(
        inference_state=inference_state,
        frame_idx=ann_frame_idx,
        obj_id=ann_obj_id,
        points=points,
        labels=labels,
        import_from_onnx=import_from_onnx,
        export_to_onnx=export_to_onnx,
        import_from_tflite=import_from_tflite,
        export_to_tflite=export_to_tflite,
        model_id=model_id
    )
    out_obj_ids.extend(obj_ids)
    out_mask_logits.extend(mask_logits)

# Process boxes if available
if len(boxes) > 0:
    print(f"Adding {len(boxes)} boxes to predictor...")
    for i, box in enumerate(boxes):
        box_obj_id = ann_obj_id + len(out_obj_ids)  # Use different object ID for each box
        _, obj_ids, mask_logits = predictor.add_new_points_or_box(
            inference_state=inference_state,
            frame_idx=ann_frame_idx,
            obj_id=box_obj_id,
            box=np.array(box, dtype=np.float32),
            import_from_onnx=import_from_onnx,
            export_to_onnx=export_to_onnx,
            import_from_tflite=import_from_tflite,
            export_to_tflite=export_to_tflite,
            model_id=model_id
        )
        out_obj_ids.extend(obj_ids)
        out_mask_logits.extend(mask_logits)

# show the results on the current (interacted) frame
plt.figure(figsize=(9, 6))
plt.title(f"frame {ann_frame_idx}")
plt.imshow(Image.open(os.path.join(video_dir, frame_names[ann_frame_idx])))
show_points(points, labels, plt.gca())
show_mask((out_mask_logits[0] > 0.0).cpu().numpy(), plt.gca(), obj_id=out_obj_ids[0])
#plt.show()
plt.savefig(f'output/video_'+model_id+'.png')

# run propagation throughout the video and collect the results in a dict
video_segments = {}  # video_segments contains the per-frame segmentation results
for out_frame_idx, out_obj_ids, out_mask_logits in predictor.propagate_in_video(inference_state, import_from_onnx=import_from_onnx, export_to_onnx=export_to_onnx, import_from_tflite=import_from_tflite, export_to_tflite=export_to_tflite, model_id=model_id):
    video_segments[out_frame_idx] = {
        out_obj_id: (out_mask_logits[i] > 0.0).cpu().numpy()
        for i, out_obj_id in enumerate(out_obj_ids)
    }

# render the segmentation results every few frames
vis_frame_stride = 1
plt.close("all")
for out_frame_idx in range(0, len(frame_names), vis_frame_stride):
    plt.figure(figsize=(6, 4))
    plt.title(f"frame {out_frame_idx}")
    plt.imshow(Image.open(os.path.join(video_dir, frame_names[out_frame_idx])))
    for out_obj_id, out_mask in video_segments[out_frame_idx].items():
        show_mask(out_mask, plt.gca(), obj_id=out_obj_id)
    #plt.show()
    plt.savefig(f'output/video{out_frame_idx+1}_'+model_id+'.png')
