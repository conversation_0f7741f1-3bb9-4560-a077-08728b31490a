#!/usr/bin/env python3
"""
Test script to check SAM 2.1 compatibility with the axinc-ai ONNX export fork
"""

import os
import torch
import traceback
from sam2.build_sam import build_sam2, _load_checkpoint
from hydra import initialize_config_module, compose
from hydra.core.global_hydra import GlobalHydra
from omegaconf import OmegaConf
from hydra.utils import instantiate
import yaml

def build_sam2_from_config_file(config_file_path, ckpt_path=None, device="cuda", mode="eval", image_size=1024):
    """Build SAM2 model directly from config file path (for SAM 2.1 configs)"""

    # Load config file directly
    with open(config_file_path, 'r') as f:
        config_dict = yaml.safe_load(f)

    # Convert to OmegaConf
    cfg = OmegaConf.create(config_dict)
    cfg.model.image_size = image_size
    OmegaConf.resolve(cfg)

    # Instantiate model
    model = instantiate(cfg.model, _recursive_=True)

    # Load checkpoint if provided
    if ckpt_path:
        _load_checkpoint(model, ckpt_path)

    model = model.to(device)
    if mode == "eval":
        model.eval()

    return model

def setup_sam21_hydra():
    """Setup Hydra to use SAM 2.1 configs"""
    try:
        # Clear any existing Hydra instance
        GlobalHydra.instance().clear()
        # Initialize with sam2.1_configs
        initialize_config_module("sam2.1_configs", version_base="1.2")
        return True
    except Exception as e:
        print(f"Failed to setup SAM 2.1 Hydra: {e}")
        return False

def test_sam21_config_loading():
    """Test loading SAM 2.1 configs with SAM 2.0 checkpoints"""

    print("=== Testing SAM 2.1 Config Compatibility ===\n")

    # Setup Hydra for SAM 2.1 configs
    if not setup_sam21_hydra():
        return {"error": "Failed to setup SAM 2.1 Hydra configuration"}
    
    # Test configurations
    test_configs = [
        ("sam2.1_hiera_t.yaml", "./checkpoints/sam2_hiera_tiny.pt", "hiera_t"),
        ("sam2.1_hiera_s.yaml", "./checkpoints/sam2_hiera_small.pt", "hiera_s"),
        ("sam2.1_hiera_b+.yaml", "./checkpoints/sam2_hiera_base_plus.pt", "hiera_b+"),
        ("sam2.1_hiera_l.yaml", "./checkpoints/sam2_hiera_large.pt", "hiera_l"),
    ]
    
    results = {}
    
    for config_file, checkpoint_path, model_name in test_configs:
        print(f"Testing {model_name} with config: {config_file}")
        print(f"Checkpoint: {checkpoint_path}")
        
        try:
            # Check if checkpoint exists
            if not os.path.exists(checkpoint_path):
                print(f"❌ Checkpoint not found: {checkpoint_path}")
                results[model_name] = {"status": "checkpoint_missing", "error": f"Checkpoint not found: {checkpoint_path}"}
                print()
                continue
            
            # Try to build the model using direct config file loading
            config_file_path = os.path.join("sam2.1_configs", config_file)
            model = build_sam2_from_config_file(
                config_file_path=config_file_path,
                ckpt_path=checkpoint_path,
                device="cpu",
                image_size=1024
            )
            
            print(f"✅ Successfully loaded {model_name} with SAM 2.1 config")
            results[model_name] = {"status": "success", "model": model}
            
        except Exception as e:
            print(f"❌ Failed to load {model_name}: {str(e)}")
            results[model_name] = {"status": "error", "error": str(e), "traceback": traceback.format_exc()}
        
        print()
    
    return results

def test_sam21_checkpoint_loading():
    """Test loading SAM 2.1 checkpoints with SAM 2.0 configs"""
    
    print("=== Testing SAM 2.1 Checkpoint Compatibility ===\n")
    
    # Test configurations
    test_configs = [
        ("sam2_hiera_t.yaml", "./checkpoints/sam2.1_hiera_tiny.pt", "hiera_t"),
        ("sam2_hiera_s.yaml", "./checkpoints/sam2.1_hiera_small.pt", "hiera_s"),
        ("sam2_hiera_b+.yaml", "./checkpoints/sam2.1_hiera_base_plus.pt", "hiera_b+"),
        ("sam2_hiera_l.yaml", "./checkpoints/sam2.1_hiera_large.pt", "hiera_l"),
    ]
    
    results = {}
    
    for config_file, checkpoint_path, model_name in test_configs:
        print(f"Testing {model_name} with checkpoint: {checkpoint_path}")
        print(f"Config: {config_file}")
        
        try:
            # Check if checkpoint exists
            if not os.path.exists(checkpoint_path):
                print(f"❌ Checkpoint not found: {checkpoint_path}")
                results[model_name] = {"status": "checkpoint_missing", "error": f"Checkpoint not found: {checkpoint_path}"}
                print()
                continue
            
            # Try to build the model
            model = build_sam2(
                config_file=config_file,
                ckpt_path=checkpoint_path,
                device="cpu",
                image_size=1024
            )
            
            print(f"✅ Successfully loaded {model_name} with SAM 2.1 checkpoint")
            results[model_name] = {"status": "success", "model": model}
            
        except Exception as e:
            print(f"❌ Failed to load {model_name}: {str(e)}")
            results[model_name] = {"status": "error", "error": str(e), "traceback": traceback.format_exc()}
        
        print()
    
    return results

def test_sam21_full_compatibility():
    """Test loading SAM 2.1 configs with SAM 2.1 checkpoints"""

    print("=== Testing SAM 2.1 Full Compatibility ===\n")

    # Setup Hydra for SAM 2.1 configs
    if not setup_sam21_hydra():
        return {"error": "Failed to setup SAM 2.1 Hydra configuration"}
    
    # Test configurations
    test_configs = [
        ("sam2.1_hiera_t.yaml", "./checkpoints/sam2.1_hiera_tiny.pt", "hiera_t"),
        ("sam2.1_hiera_s.yaml", "./checkpoints/sam2.1_hiera_small.pt", "hiera_s"),
        ("sam2.1_hiera_b+.yaml", "./checkpoints/sam2.1_hiera_base_plus.pt", "hiera_b+"),
        ("sam2.1_hiera_l.yaml", "./checkpoints/sam2.1_hiera_large.pt", "hiera_l"),
    ]
    
    results = {}
    
    for config_file, checkpoint_path, model_name in test_configs:
        print(f"Testing {model_name} with SAM 2.1 config and checkpoint")
        print(f"Config: {config_file}")
        print(f"Checkpoint: {checkpoint_path}")
        
        try:
            # Check if checkpoint exists
            if not os.path.exists(checkpoint_path):
                print(f"❌ Checkpoint not found: {checkpoint_path}")
                results[model_name] = {"status": "checkpoint_missing", "error": f"Checkpoint not found: {checkpoint_path}"}
                print()
                continue
            
            # Try to build the model using direct config file loading
            config_file_path = os.path.join("sam2.1_configs", config_file)
            model = build_sam2_from_config_file(
                config_file_path=config_file_path,
                ckpt_path=checkpoint_path,
                device="cpu",
                image_size=1024
            )
            
            print(f"✅ Successfully loaded {model_name} with SAM 2.1 config and checkpoint")
            results[model_name] = {"status": "success", "model": model}
            
        except Exception as e:
            print(f"❌ Failed to load {model_name}: {str(e)}")
            results[model_name] = {"status": "error", "error": str(e), "traceback": traceback.format_exc()}
        
        print()
    
    return results

def analyze_state_dict_differences():
    """Analyze differences between SAM 2.0 and SAM 2.1 state dictionaries"""
    
    print("=== Analyzing State Dictionary Differences ===\n")
    
    sam20_checkpoint = "./checkpoints/sam2_hiera_tiny.pt"
    sam21_checkpoint = "./checkpoints/sam2.1_hiera_tiny.pt"
    
    if not os.path.exists(sam20_checkpoint):
        print(f"❌ SAM 2.0 checkpoint not found: {sam20_checkpoint}")
        return None
    
    if not os.path.exists(sam21_checkpoint):
        print(f"❌ SAM 2.1 checkpoint not found: {sam21_checkpoint}")
        return None
    
    try:
        # Load state dictionaries
        sam20_state = torch.load(sam20_checkpoint, map_location="cpu")
        sam21_state = torch.load(sam21_checkpoint, map_location="cpu")
        
        sam20_keys = set(sam20_state.keys())
        sam21_keys = set(sam21_state.keys())
        
        # Find differences
        only_in_sam20 = sam20_keys - sam21_keys
        only_in_sam21 = sam21_keys - sam20_keys
        common_keys = sam20_keys & sam21_keys
        
        print(f"SAM 2.0 keys: {len(sam20_keys)}")
        print(f"SAM 2.1 keys: {len(sam21_keys)}")
        print(f"Common keys: {len(common_keys)}")
        print(f"Only in SAM 2.0: {len(only_in_sam20)}")
        print(f"Only in SAM 2.1: {len(only_in_sam21)}")
        print()
        
        if only_in_sam20:
            print("Keys only in SAM 2.0:")
            for key in sorted(only_in_sam20):
                print(f"  - {key}")
            print()
        
        if only_in_sam21:
            print("Keys only in SAM 2.1:")
            for key in sorted(only_in_sam21):
                print(f"  - {key}")
            print()
        
        # Check for shape differences in common keys
        shape_differences = []
        for key in common_keys:
            if hasattr(sam20_state[key], 'shape') and hasattr(sam21_state[key], 'shape'):
                if sam20_state[key].shape != sam21_state[key].shape:
                    shape_differences.append((key, sam20_state[key].shape, sam21_state[key].shape))
        
        if shape_differences:
            print("Shape differences in common keys:")
            for key, sam20_shape, sam21_shape in shape_differences:
                print(f"  - {key}: SAM 2.0 {sam20_shape} vs SAM 2.1 {sam21_shape}")
            print()
        
        return {
            "sam20_keys": sam20_keys,
            "sam21_keys": sam21_keys,
            "only_in_sam20": only_in_sam20,
            "only_in_sam21": only_in_sam21,
            "common_keys": common_keys,
            "shape_differences": shape_differences
        }
        
    except Exception as e:
        print(f"❌ Failed to analyze state dictionaries: {str(e)}")
        print(traceback.format_exc())
        return None

if __name__ == "__main__":
    print("SAM 2.1 Compatibility Testing\n")
    
    # Test 1: SAM 2.1 configs with SAM 2.0 checkpoints
    config_results = test_sam21_config_loading()
    
    # Test 2: SAM 2.0 configs with SAM 2.1 checkpoints  
    checkpoint_results = test_sam21_checkpoint_loading()
    
    # Test 3: SAM 2.1 configs with SAM 2.1 checkpoints
    full_results = test_sam21_full_compatibility()
    
    # Test 4: Analyze state dictionary differences
    state_dict_analysis = analyze_state_dict_differences()
    
    print("=== Summary ===")
    print(f"Config compatibility tests: {len([r for r in config_results.values() if r['status'] == 'success'])}/{len(config_results)} passed")
    print(f"Checkpoint compatibility tests: {len([r for r in checkpoint_results.values() if r['status'] == 'success'])}/{len(checkpoint_results)} passed")
    print(f"Full SAM 2.1 tests: {len([r for r in full_results.values() if r['status'] == 'success'])}/{len(full_results)} passed")
