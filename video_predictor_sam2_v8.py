#!/usr/bin/env python3
"""
SAM2 Video Predictor Standalone Script v6

Enhanced version with text prompt-based object detection capabilities from Grounded-SAM-2.
Combines automatic text-based detection with manual refinement capabilities.

Features:
- Text prompt-based automatic object detection using Grounding DINO + SAM2
- Configurable frame selection for Grounded SAM 2 prompting (--prompt-frame)
- PNG mask visualization files alongside existing .npy mask files
- Automatic video-to-frame conversion using video2frame.py
- Multiple SAM2 model size support (tiny, small, base_plus, large)
- Configurable model selection via command-line arguments
- Adjustable input size for Video Segmentation model
- Video creation functionality from processed image sequences
- Directory structure management (video_dir, mask_data_dir, json_data_dir, result_dir)
- Interactive click-based segmentation with manual refinement
- Bounding box support
- Multi-object tracking
- Frame-by-frame processing
- Mask visualization and export
- Reset state functionality that preserves automatic annotations

Requirements:
- SAM2 environment with all dependencies installed
- Grounded-SAM-2 with Grounding DINO support
- CUDA-capable GPU (recommended)
- Proper PYTHONPATH configuration

Usage:
    # Text-based automatic detection
    PYTHONPATH="/c/Users/<USER>/Codings/sam2davis/sam2:/c/Users/<USER>/Codings/sam2davis:$PYTHONPATH" \
    "/c/Users/<USER>/.conda/envs/sam2_env_py310/python.exe" \
    video_predictor_sam2_v6.py --video path/to/video.mp4 --text-prompt "person. car. dog." --model base_plus

    # Text-based detection with specific prompt frame
    PYTHONPATH="/c/Users/<USER>/Codings/sam2davis/sam2:/c/Users/<USER>/Codings/sam2davis:$PYTHONPATH" \
    "/c/Users/<USER>/.conda/envs/sam2_env_py310/python.exe" \
    video_predictor_sam2_v6.py --video path/to/video.mp4 --text-prompt "skirt." --prompt-frame 10 --allow-refinement

    # Manual annotation mode (original functionality)
    PYTHONPATH="/c/Users/<USER>/Codings/sam2davis/sam2:/c/Users/<USER>/Codings/sam2davis:$PYTHONPATH" \
    "/c/Users/<USER>/.conda/envs/sam2_env_py310/python.exe" \
    video_predictor_sam2_v6.py --video path/to/video.mp4 --model base_plus --manual-mode

Author: Enhanced from Meta's SAM2 video_predictor_example.ipynb with Grounded-SAM-2 integration
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
import numpy as np
import torch
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
from matplotlib.widgets import Button, TextBox
from PIL import Image
import cv2
from tqdm import tqdm
import warnings
import shutil
import json
from dataclasses import dataclass, field
from typing import Dict, Any

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

# Environment setup for Apple MPS fallback
os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"

# Add current directory to Python path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Add Grounded-SAM-2 directory to path if it exists
grounded_sam2_dir = os.path.join(current_dir, "Grounded-SAM-2")
if os.path.exists(grounded_sam2_dir) and grounded_sam2_dir not in sys.path:
    sys.path.insert(0, grounded_sam2_dir)

# SAM2 imports
try:
    from sam2.build_sam import build_sam2_video_predictor, build_sam2
    from sam2.sam2_image_predictor import SAM2ImagePredictor
except ImportError as e:
    print(f"SAM2 import error: {e}")
    print("Please ensure you're running from the correct directory with proper PYTHONPATH")
    print("Expected PYTHONPATH format:")
    print('PYTHONPATH="/path/to/sam2davis/sam2:/path/to/sam2davis:$PYTHONPATH"')
    print(f"Current directory: {current_dir}")
    print(f"Grounded-SAM-2 directory: {grounded_sam2_dir}")
    print(f"Python path: {sys.path}")
    sys.exit(1)

# Grounding DINO imports (optional for text-based detection)
try:
    from transformers import AutoProcessor, AutoModelForZeroShotObjectDetection
    GROUNDING_DINO_AVAILABLE = True
    print("[OK] Grounding DINO (HuggingFace) imports available")
except ImportError as e:
    GROUNDING_DINO_AVAILABLE = False
    print(f"[WARN] Grounding DINO imports not available: {e}")
    print("Text-based detection will be disabled. Install with:")
    print("pip install transformers")

# Grounded-SAM-2 utility imports (optional)
try:
    from utils.mask_dictionary_model import MaskDictionaryModel as GroundedMaskDictionaryModel
    from utils.mask_dictionary_model import ObjectInfo as GroundedObjectInfo
    GROUNDED_UTILS_AVAILABLE = True
    print("[OK] Grounded-SAM-2 utilities available")
except ImportError as e:
    GROUNDED_UTILS_AVAILABLE = False
    print(f"[WARN] Grounded-SAM-2 utilities not available: {e}")
    print("Using built-in mask dictionary model")

# Supervision imports (optional for fast visualization)
try:
    import supervision as sv
    SUPERVISION_AVAILABLE = True
    print("[OK] Supervision library available for fast visualization")
except ImportError as e:
    SUPERVISION_AVAILABLE = False
    print(f"[WARN] Supervision library not available: {e}")
    print("Using matplotlib for visualization (slower)")


@dataclass
class ObjectInfo:
    """Object information for tracking."""
    mask: np.ndarray = None
    box: list = field(default_factory=list)
    object_id: int = 0
    confidence: float = 0.0

    def update_box(self):
        """Update bounding box from mask."""
        if self.mask is not None:
            # Find bounding box from mask
            rows = np.any(self.mask, axis=1)
            cols = np.any(self.mask, axis=0)

            if rows.any() and cols.any():
                rmin, rmax = np.where(rows)[0][[0, -1]]
                cmin, cmax = np.where(cols)[0][[0, -1]]
                self.box = [int(cmin), int(rmin), int(cmax), int(rmax)]
            else:
                self.box = [0, 0, 0, 0]

    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        return {
            'object_id': self.object_id,
            'box': self.box,
            'confidence': self.confidence,
            'mask_shape': list(self.mask.shape) if self.mask is not None else None
        }


@dataclass
class MaskDictionaryModel:
    """Model for storing mask and object information."""
    mask_name: str = ""
    mask_height: int = 1080
    mask_width: int = 1920
    promote_type: str = "mask"
    labels: Dict[int, ObjectInfo] = field(default_factory=dict)

    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        labels_dict = {}
        for obj_id, obj_info in self.labels.items():
            labels_dict[str(obj_id)] = obj_info.to_dict()

        return {
            'mask_name': self.mask_name,
            'mask_height': self.mask_height,
            'mask_width': self.mask_width,
            'promote_type': self.promote_type,
            'labels': labels_dict
        }

    def to_json(self, json_file):
        """Save to JSON file."""
        with open(json_file, "w") as f:
            json.dump(self.to_dict(), f, indent=4)

    def from_json(self, json_file):
        """Load from JSON file."""
        with open(json_file, "r") as f:
            data = json.load(f)
            self.mask_name = data.get("mask_name", "")
            self.mask_height = data.get("mask_height", 1080)
            self.mask_width = data.get("mask_width", 1920)
            self.promote_type = data.get("promote_type", "mask")

            # Load labels
            self.labels = {}
            for obj_id_str, obj_data in data.get("labels", {}).items():
                obj_id = int(obj_id_str)
                obj_info = ObjectInfo(
                    object_id=obj_data.get("object_id", obj_id),
                    box=obj_data.get("box", []),
                    confidence=obj_data.get("confidence", 0.0)
                )
                self.labels[obj_id] = obj_info

        return self

    def save_empty_mask_and_json(self, mask_data_dir, json_data_dir, image_name_list=None):
        """Save empty mask and JSON files."""
        mask_img = np.zeros((self.mask_height, self.mask_width), dtype=np.uint16)

        if image_name_list:
            for image_name in image_name_list:
                image_base_name = Path(image_name).stem
                mask_name = f"mask_{image_base_name}.npy"

                # Save empty mask
                np.save(os.path.join(mask_data_dir, mask_name), mask_img)

                # Save empty JSON
                empty_model = MaskDictionaryModel(
                    mask_name=mask_name,
                    mask_height=self.mask_height,
                    mask_width=self.mask_width
                )
                json_path = os.path.join(json_data_dir, f"mask_{image_base_name}.json")
                empty_model.to_json(json_path)


def create_dirs(path):
    """
    Ensure the given path exists. If it does not exist, create it using os.makedirs.
    
    Args:
        path (str): The directory path to check or create.
    """
    try: 
        if not os.path.exists(path):
            os.makedirs(path, exist_ok=True)
            print(f"Path '{path}' did not exist and has been created.")
        else:
            print(f"Path '{path}' already exists.")
    except Exception as e:
        print(f"An error occurred while creating the path: {e}")


def create_video_from_images(image_folder, output_video_path, frame_rate=25):
    """
    Create a video from a sequence of images.
    
    Args:
        image_folder (str): Path to folder containing images
        output_video_path (str): Path for output video file
        frame_rate (int): Frame rate for the output video
    """
    # define valid extension
    valid_extensions = [".jpg", ".jpeg", ".JPG", ".JPEG", ".png", ".PNG"]
    
    # get all image files in the folder
    image_files = [f for f in os.listdir(image_folder) 
                   if os.path.splitext(f)[1] in valid_extensions]
    image_files.sort()  # sort the files in alphabetical order
    print(f"Found {len(image_files)} images for video creation")
    
    if not image_files:
        raise ValueError("No valid image files found in the specified folder.")
    
    # load the first image to get the dimensions of the video
    first_image_path = os.path.join(image_folder, image_files[0])
    first_image = cv2.imread(first_image_path)
    height, width, _ = first_image.shape
    
    # create a video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v') # codec for saving the video
    video_writer = cv2.VideoWriter(output_video_path, fourcc, frame_rate, (width, height))
    
    # write each image to the video
    for image_file in tqdm(image_files, desc="Creating video"):
        image_path = os.path.join(image_folder, image_file)
        image = cv2.imread(image_path)
        video_writer.write(image)
    
    # source release
    video_writer.release()
    print(f"Video saved at {output_video_path}")


class DirectoryManager:
    """Manages directory structure for video processing workflow."""
    
    def __init__(self, base_output_dir):
        """
        Initialize directory manager.
        
        Args:
            base_output_dir (str): Base output directory for all processing results
        """
        self.base_output_dir = Path(base_output_dir)
        self.video_dir = None
        self.mask_data_dir = None
        self.json_data_dir = None
        self.result_dir = None
        
    def setup_directories(self, video_name=None):
        """
        Setup directory structure for video processing.
        
        Args:
            video_name (str): Optional video name for subdirectory organization
        """
        if video_name:
            self.base_output_dir = self.base_output_dir / video_name
            
        # Create main output directory
        create_dirs(str(self.base_output_dir))
        
        # Setup subdirectories
        self.mask_data_dir = self.base_output_dir / "mask_data"
        self.json_data_dir = self.base_output_dir / "json_data"
        self.result_dir = self.base_output_dir / "result"
        
        # Create all subdirectories
        create_dirs(str(self.mask_data_dir))
        create_dirs(str(self.json_data_dir))
        create_dirs(str(self.result_dir))
        
        print(f"Directory structure created:")
        print(f"  Base: {self.base_output_dir}")
        print(f"  Masks: {self.mask_data_dir}")
        print(f"  JSON: {self.json_data_dir}")
        print(f"  Results: {self.result_dir}")
        
    def set_video_dir(self, video_dir):
        """Set the video directory path."""
        self.video_dir = Path(video_dir)
        print(f"Video directory set to: {self.video_dir}")
        
    def get_paths(self):
        """Get all directory paths as a dictionary."""
        return {
            'base_output_dir': str(self.base_output_dir),
            'video_dir': str(self.video_dir) if self.video_dir else None,
            'mask_data_dir': str(self.mask_data_dir),
            'json_data_dir': str(self.json_data_dir),
            'result_dir': str(self.result_dir)
        }


def enhance_class_name(class_name):
    """
    Enhance class names for better Grounding DINO detection through prompt engineering.

    This function applies various prompt engineering techniques to improve object detection
    accuracy by providing more descriptive and contextually rich text prompts.

    Args:
        class_name (str): Original class name (e.g., "person", "car", "skirt")

    Returns:
        str: Enhanced class name with better descriptive context

    Examples:
        enhance_class_name("person") -> "person human being individual"
        enhance_class_name("car") -> "car vehicle automobile"
        enhance_class_name("skirt") -> "skirt clothing garment dress"
    """
    # Remove extra whitespace and convert to lowercase
    class_name = class_name.strip().lower()

    # Dictionary of enhanced prompts for common objects
    enhancement_dict = {
        # People and body parts
        "person": "person human being individual people",
        "people": "people persons humans individuals group",
        "man": "man male person human being individual",
        "woman": "woman female person human being individual",
        "child": "child kid young person human being",
        "baby": "baby infant child young person",
        "face": "face human face person head",
        "hand": "hand human hand person arm",
        "head": "head human head person face",

        # Vehicles
        "car": "car vehicle automobile motor car",
        "truck": "truck vehicle large vehicle motor vehicle",
        "bus": "bus vehicle public transport large vehicle",
        "motorcycle": "motorcycle motorbike bike vehicle",
        "bicycle": "bicycle bike cycle vehicle",
        "train": "train locomotive railway vehicle",
        "airplane": "airplane aircraft plane flying vehicle",
        "boat": "boat ship vessel watercraft",

        # Animals
        "dog": "dog canine animal pet domestic animal",
        "cat": "cat feline animal pet domestic animal",
        "bird": "bird flying animal creature",
        "horse": "horse equine animal large animal",
        "cow": "cow cattle animal farm animal",
        "sheep": "sheep animal farm animal livestock",

        # Clothing and accessories
        "skirt": "skirt clothing garment dress wear",
        "shirt": "shirt clothing garment top wear",
        "pants": "pants trousers clothing garment wear",
        "dress": "dress clothing garment wear outfit",
        "hat": "hat headwear clothing accessory",
        "shoes": "shoes footwear clothing accessory",
        "bag": "bag handbag purse accessory",
        "backpack": "backpack bag luggage accessory",

        # Sports and activities
        "ball": "ball sports equipment round object",
        "tennis": "tennis sport racket game",
        "soccer": "soccer football sport ball game",
        "basketball": "basketball sport ball game",
        "baseball": "baseball sport ball game",

        # Food and kitchen
        "food": "food meal dish cuisine",
        "pizza": "pizza food meal dish",
        "cake": "cake dessert food sweet",
        "apple": "apple fruit food",
        "banana": "banana fruit food",
        "bottle": "bottle container vessel",
        "cup": "cup mug container vessel",
        "plate": "plate dish container tableware",

        # Furniture and household
        "chair": "chair seat furniture",
        "table": "table furniture surface",
        "bed": "bed furniture sleeping",
        "sofa": "sofa couch furniture seating",
        "tv": "tv television screen monitor",
        "laptop": "laptop computer device electronics",
        "phone": "phone mobile device electronics",
        "book": "book reading material publication",

        # Nature and outdoor
        "tree": "tree plant vegetation nature",
        "flower": "flower plant bloom nature",
        "grass": "grass vegetation plant nature",
        "sky": "sky atmosphere heavens",
        "cloud": "cloud sky weather atmosphere",
        "water": "water liquid fluid",
        "building": "building structure architecture",
        "house": "house home building structure",

        # Transportation infrastructure
        "road": "road street path way",
        "bridge": "bridge structure crossing",
        "traffic": "traffic vehicles cars road",
        "sign": "sign signage text information",
        "light": "light illumination lamp",

        # Technology
        "computer": "computer device electronics technology",
        "screen": "screen monitor display device",
        "camera": "camera device photography equipment",
        "remote": "remote control device electronics",
    }

    # Check if we have a specific enhancement for this class
    if class_name in enhancement_dict:
        return enhancement_dict[class_name]

    # For unknown classes, apply general enhancement patterns
    enhanced = class_name

    # Add generic descriptors based on common patterns
    if any(keyword in class_name for keyword in ["tool", "equipment", "device"]):
        enhanced += " tool equipment device object"
    elif any(keyword in class_name for keyword in ["clothing", "wear", "garment"]):
        enhanced += " clothing garment wear item"
    elif any(keyword in class_name for keyword in ["food", "meal", "dish"]):
        enhanced += " food item edible"
    elif any(keyword in class_name for keyword in ["animal", "pet"]):
        enhanced += " animal creature living being"
    elif any(keyword in class_name for keyword in ["vehicle", "transport"]):
        enhanced += " vehicle transportation"
    elif any(keyword in class_name for keyword in ["furniture"]):
        enhanced += " furniture item object"
    elif any(keyword in class_name for keyword in ["plant", "vegetation"]):
        enhanced += " plant vegetation nature"
    else:
        # Generic enhancement - add "object" and "item" for better detection
        enhanced += f" {class_name} object item"

    return enhanced


class GroundingDINODetector:
    """Grounding DINO text-based object detector for automatic annotation."""

    def __init__(self, device=None, model_id="IDEA-Research/grounding-dino-tiny"):
        """
        Initialize Grounding DINO detector.

        Args:
            device (str): Device to use - cuda, cpu, or mps
            model_id (str): HuggingFace model ID for Grounding DINO
        """
        if not GROUNDING_DINO_AVAILABLE:
            raise RuntimeError("Grounding DINO not available. Install transformers package.")

        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        self.model_id = model_id
        self.processor = None
        self.model = None
        self.sam_image_predictor = None

        print(f"Initializing Grounding DINO detector...")
        print(f"Model ID: {model_id}")
        print(f"Device: {self.device}")

        self._load_models()

    def _load_models(self):
        """Load Grounding DINO and SAM image predictor models."""
        try:
            # Load Grounding DINO
            print("Loading Grounding DINO processor...")
            self.processor = AutoProcessor.from_pretrained(self.model_id)

            print("Loading Grounding DINO model...")
            self.model = AutoModelForZeroShotObjectDetection.from_pretrained(self.model_id).to(self.device)

            print("[OK] Grounding DINO loaded successfully")

        except Exception as e:
            print(f"[ERROR] Failed to load Grounding DINO: {e}")
            raise

    def detect_objects(self, image, text_prompt, box_threshold=0.25, text_threshold=0.25, use_enhanced_prompts=True):
        """
        Detect objects in image using text prompt with optional prompt enhancement.

        Args:
            image (PIL.Image): Input image
            text_prompt (str): Text description of objects to detect
            box_threshold (float): Box confidence threshold
            text_threshold (float): Text confidence threshold
            use_enhanced_prompts (bool): Whether to use enhanced prompts for better detection

        Returns:
            dict: Detection results with boxes and labels
        """
        if self.model is None or self.processor is None:
            raise RuntimeError("Models not loaded. Call _load_models() first.")

        # Apply prompt enhancement if enabled
        if use_enhanced_prompts:
            enhanced_prompt = self._enhance_text_prompt(text_prompt)
            print(f"Original prompt: '{text_prompt}'")
            print(f"Enhanced prompt: '{enhanced_prompt}'")
            text_prompt = enhanced_prompt
        else:
            print(f"Detecting objects with prompt: '{text_prompt}'")

        # Process image and text
        inputs = self.processor(images=image, text=text_prompt, return_tensors="pt").to(self.device)

        # Run detection
        with torch.no_grad():
            outputs = self.model(**inputs)

        # Post-process results
        results = self.processor.post_process_grounded_object_detection(
            outputs,
            inputs.input_ids,
            box_threshold=box_threshold,
            text_threshold=text_threshold,
            target_sizes=[image.size[::-1]]
        )

        if len(results) > 0 and len(results[0]["boxes"]) > 0:
            print(f"[OK] Detected {len(results[0]['boxes'])} objects")
            return {
                'boxes': results[0]["boxes"],
                'labels': results[0]["labels"],
                'scores': results[0].get("scores", [])
            }
        else:
            print("[WARN] No objects detected")
            return {'boxes': [], 'labels': [], 'scores': []}

    def _enhance_text_prompt(self, text_prompt):
        """
        Enhance text prompt using the enhance_class_name function for better detection.

        Args:
            text_prompt (str): Original text prompt (e.g., "person. car. skirt.")

        Returns:
            str: Enhanced text prompt with better descriptive context
        """
        # Split the prompt by periods and process each class
        classes = [cls.strip() for cls in text_prompt.split('.') if cls.strip()]

        # Enhance each class name
        enhanced_classes = []
        for class_name in classes:
            enhanced = enhance_class_name(class_name)
            enhanced_classes.append(enhanced)

        # Join back with periods
        enhanced_prompt = '. '.join(enhanced_classes) + '.'

        return enhanced_prompt


class SAM2VideoPredictor:
    """Enhanced SAM2 Video Predictor class with configurable model support and text-based detection."""

    def __init__(self, model_size="base_plus", device=None, input_size=None, enable_grounding_dino=False):
        """
        Initialize the SAM2 Video Predictor.

        Args:
            model_size (str): Model size - tiny, small, base_plus, or large
            device (str): Device to use - cuda, cpu, or mps
            input_size (int): Input size for the model (optional)
            enable_grounding_dino (bool): Whether to enable Grounding DINO for text-based detection
        """
        self.model_size = model_size
        self.device = self._setup_device(device)
        self.input_size = input_size
        self.predictor = None
        self.inference_state = None
        self.sam_image_predictor = None
        self.grounding_detector = None
        self.enable_grounding_dino = enable_grounding_dino and GROUNDING_DINO_AVAILABLE

        print(f"Initializing SAM2 Video Predictor...")
        print(f"Model size: {model_size}")
        print(f"Device: {self.device}")
        if input_size:
            print(f"Input size: {input_size}")
        if self.enable_grounding_dino:
            print("[OK] Grounding DINO text-based detection enabled")

        # Load the models
        self._load_model()

        if self.enable_grounding_dino:
            self._load_grounding_dino()
    
    def _setup_device(self, device=None):
        """Setup computation device with proper configuration."""
        if device is None:
            if torch.cuda.is_available():
                device = torch.device("cuda")
            elif torch.backends.mps.is_available():
                device = torch.device("mps")
            else:
                device = torch.device("cpu")
        else:
            device = torch.device(device)
        
        print(f"Using device: {device}")
        
        # Configure device-specific optimizations
        if device.type == "cuda":
            # Use bfloat16 for CUDA
            torch.autocast("cuda", dtype=torch.bfloat16).__enter__()
            # Enable TF32 for Ampere GPUs
            if torch.cuda.get_device_properties(0).major >= 8:
                torch.backends.cuda.matmul.allow_tf32 = True
                torch.backends.cudnn.allow_tf32 = True
                print("Enabled TF32 for Ampere GPU")
        elif device.type == "mps":
            print("Warning: MPS support is preliminary. SAM2 is trained with CUDA and might")
            print("give numerically different outputs and sometimes degraded performance on MPS.")
        
        return device

    def _find_config_and_checkpoint_paths(self):
        """Find the correct paths for SAM2 configs and checkpoints."""
        # Possible config base paths
        possible_config_bases = [
            "configs/sam2.1",  # Current directory
            "sam2/configs/sam2.1",  # sam2 subdirectory
            "../configs/sam2.1",  # Parent directory
            "Grounded-SAM-2/configs/sam2.1",  # Grounded-SAM-2 subdirectory
        ]

        # Possible checkpoint base paths
        possible_checkpoint_bases = [
            "checkpoints",  # Current directory
            "../checkpoints",  # Parent directory
            "Grounded-SAM-2/checkpoints",  # Grounded-SAM-2 subdirectory
        ]

        # Find working config path
        config_base = None
        for path in possible_config_bases:
            if os.path.exists(path):
                config_base = path
                break

        # Find working checkpoint path
        checkpoint_base = None
        for path in possible_checkpoint_bases:
            if os.path.exists(path):
                checkpoint_base = path
                break

        if not config_base:
            raise RuntimeError(f"Could not find SAM2 configs. Searched: {possible_config_bases}")
        if not checkpoint_base:
            raise RuntimeError(f"Could not find SAM2 checkpoints. Searched: {possible_checkpoint_bases}")

        print(f"Found config base: {config_base}")
        print(f"Found checkpoint base: {checkpoint_base}")

        return config_base, checkpoint_base

    def _load_model(self):
        """Load the SAM2 video predictor model with configurable checkpoint support."""
        # Use the simplified approach that works with the current environment
        # Model configurations using relative paths from the Grounded-SAM-2 working directory
        model_configs = {
            "tiny": ("configs/sam2.1/sam2.1_hiera_t.yaml", "checkpoints/sam2.1_hiera_tiny.pt"),
            "small": ("configs/sam2.1/sam2.1_hiera_s.yaml", "checkpoints/sam2.1_hiera_small.pt"),
            "base_plus": ("configs/sam2.1/sam2.1_hiera_b+.yaml", "checkpoints/sam2.1_hiera_base_plus.pt"),
            "large": ("configs/sam2.1/sam2.1_hiera_l.yaml", "checkpoints/sam2.1_hiera_large.pt"),
            "custom": ("configs/sam2.1/sam2.1_hiera_b+.yaml", "checkpoints/checkpoint.pt")
        }

        if self.model_size not in model_configs:
            raise ValueError(f"Invalid model size: {self.model_size}. Choose from: {list(model_configs.keys())}")

        model_cfg, checkpoint_path = model_configs[self.model_size]

        # Store for later use in Grounding DINO integration
        self.model_cfg = model_cfg
        self.checkpoint_path = checkpoint_path

        # Check if we need to change working directory to find configs
        original_cwd = os.getcwd()
        grounded_sam2_path = os.path.join(current_dir, "Grounded-SAM-2")

        # Try to find configs in multiple locations
        config_found = False
        checkpoint_found = False

        # Check current directory first
        if os.path.exists(model_cfg) and os.path.exists(checkpoint_path):
            config_found = True
            checkpoint_found = True
            print(f"Found configs in current directory")

        # Check Grounded-SAM-2 directory
        elif os.path.exists(grounded_sam2_path):
            grounded_config = os.path.join(grounded_sam2_path, model_cfg)
            grounded_checkpoint = os.path.join(grounded_sam2_path, checkpoint_path)

            if os.path.exists(grounded_config):
                config_found = True
                model_cfg = grounded_config
                print(f"Found config in Grounded-SAM-2: {grounded_config}")

            if os.path.exists(grounded_checkpoint):
                checkpoint_found = True
                checkpoint_path = grounded_checkpoint
                print(f"Found checkpoint in Grounded-SAM-2: {grounded_checkpoint}")

        # Check parent directory for checkpoints
        if not checkpoint_found:
            parent_checkpoint = os.path.join(current_dir, checkpoint_path)
            if os.path.exists(parent_checkpoint):
                checkpoint_found = True
                checkpoint_path = parent_checkpoint
                print(f"Found checkpoint in parent directory: {parent_checkpoint}")

        if not config_found:
            raise FileNotFoundError(f"Config file not found: {model_cfg}")
        if not checkpoint_found:
            raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")

        print(f"Loading model config: {model_cfg}")
        print(f"Loading checkpoint: {checkpoint_path}")

        # Build the video predictor with optional input size configuration
        if self.input_size:
            # Note: Input size configuration would need to be implemented in the SAM2 config
            # For now, we'll load the standard model and note the input size preference
            print(f"Note: Input size {self.input_size} requested - this may require config modification")

        # Change to Grounded-SAM-2 directory if needed for Hydra to find configs
        try:
            if os.path.exists(grounded_sam2_path) and not config_found:
                print(f"Changing working directory to: {grounded_sam2_path}")
                os.chdir(grounded_sam2_path)
                # Use relative paths from Grounded-SAM-2 directory
                model_cfg = model_configs[self.model_size][0]
                checkpoint_path = os.path.relpath(checkpoint_path, grounded_sam2_path)

            print(f"Using config path: {model_cfg}")
            print(f"Using checkpoint path: {checkpoint_path}")

            # Update stored paths for Grounding DINO integration
            self.model_cfg = model_cfg
            self.checkpoint_path = checkpoint_path

            self.predictor = build_sam2_video_predictor(model_cfg, checkpoint_path, device=self.device)
            print("SAM2 video predictor loaded successfully!")

        finally:
            # Always restore original working directory
            os.chdir(original_cwd)

    def _load_grounding_dino(self):
        """Load Grounding DINO detector and SAM image predictor for text-based detection."""
        try:
            # Initialize Grounding DINO detector
            self.grounding_detector = GroundingDINODetector(device=self.device)

            # Load SAM image predictor using the same model config as video predictor
            print("Loading SAM2 image predictor for text-based detection...")
            sam2_image_model = build_sam2(self.model_cfg, self.checkpoint_path, device=self.device)
            self.sam_image_predictor = SAM2ImagePredictor(sam2_image_model)

            print("[OK] Grounding DINO and SAM image predictor loaded successfully")

        except Exception as e:
            print(f"[ERROR] Failed to load Grounding DINO components: {e}")
            self.enable_grounding_dino = False
            self.grounding_detector = None
            self.sam_image_predictor = None

    def get_model_info(self):
        """Get information about the loaded model."""
        return {
            'model_size': self.model_size,
            'device': str(self.device),
            'input_size': self.input_size,
            'model_loaded': self.predictor is not None,
            'grounding_dino_enabled': self.enable_grounding_dino,
            'grounding_dino_loaded': self.grounding_detector is not None
        }

    def preprocess_video(self, video_path, max_frames=100, output_dir=None):
        """
        Convert video to frames using the video2frame.py script.

        Args:
            video_path (str): Path to the input video file
            max_frames (int): Maximum number of frames to extract
            output_dir (str): Output directory for frames (optional)

        Returns:
            str: Path to the directory containing extracted frames
        """
        video_path = Path(video_path)

        if not video_path.exists():
            raise FileNotFoundError(f"Video file not found: {video_path}")

        print(f"Preprocessing video: {video_path}")

        # Find video2frame.py script
        video2frame_script = None
        possible_paths = [
            "video2frame.py",  # Current directory
            "../video2frame.py",  # Parent directory
            os.path.join(current_dir, "video2frame.py"),  # Script directory
            os.path.join(os.path.dirname(current_dir), "video2frame.py"),  # Parent of script directory
        ]

        for path in possible_paths:
            if os.path.exists(path):
                video2frame_script = path
                break

        if not video2frame_script:
            raise FileNotFoundError(f"video2frame.py script not found. Searched: {possible_paths}")

        # Prepare video2frame command
        cmd = [
            sys.executable, video2frame_script,
            "--input", str(video_path),
            "--max-frames", str(max_frames)
        ]

        if output_dir:
            cmd.extend(["--output_dir", output_dir])

        # Run video2frame.py
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("Video preprocessing completed successfully!")
            print(result.stdout)
        except subprocess.CalledProcessError as e:
            print(f"Error during video preprocessing: {e}")
            print(f"stdout: {e.stdout}")
            print(f"stderr: {e.stderr}")
            raise

        # Determine output directory
        if output_dir:
            frames_dir = Path(output_dir) / f"{video_path.stem}_frames"
        else:
            frames_dir = video_path.parent / f"{video_path.stem}_frames"

        if not frames_dir.exists():
            raise RuntimeError(f"Expected frames directory not found: {frames_dir}")

        print(f"Frames extracted to: {frames_dir}")
        return str(frames_dir)

    def init_video_state(self, video_frames_dir):
        """
        Initialize inference state for the video.

        Args:
            video_frames_dir (str): Directory containing video frames

        Returns:
            dict: Inference state for the video
        """
        print(f"Initializing video state for: {video_frames_dir}")

        # Store for reset functionality
        self.video_frames_dir = video_frames_dir

        # Initialize inference state
        self.inference_state = self.predictor.init_state(video_path=video_frames_dir)

        print("Video state initialized successfully!")
        return self.inference_state

    def add_click_annotation(self, frame_idx, points, labels, obj_id=1):
        """
        Add click annotations to a frame.

        Args:
            frame_idx (int): Frame index (0-based)
            points (list): List of [x, y] coordinates
            labels (list): List of labels (1 for positive, 0 for negative)
            obj_id (int): Object ID for tracking

        Returns:
            tuple: (out_obj_ids, out_mask_logits) from the predictor
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        points = np.array(points, dtype=np.float32)
        labels = np.array(labels, dtype=np.int32)

        print(f"Adding click annotation to frame {frame_idx}")
        print(f"Points: {points}")
        print(f"Labels: {labels}")
        print(f"Object ID: {obj_id}")

        # Debug: Check for negative clicks
        positive_count = np.sum(labels == 1)
        negative_count = np.sum(labels == 0)
        print(f"Debug: {positive_count} positive clicks, {negative_count} negative clicks")

        # Debug: Show which points are negative
        for i, (point, label) in enumerate(zip(points, labels)):
            label_text = "positive" if label == 1 else "negative"
            print(f"  Point {i+1}: ({point[0]:.1f}, {point[1]:.1f}) - {label_text}")

        # Add points to the predictor
        _, out_obj_ids, out_mask_logits = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            points=points,
            labels=labels,
        )

        # Debug: Check mask logits statistics
        if len(out_mask_logits) > 0:
            mask_logits = out_mask_logits[0]  # First object's mask
            mask_min = float(mask_logits.min())
            mask_max = float(mask_logits.max())
            mask_mean = float(mask_logits.mean())
            print(f"Debug: Mask logits - min: {mask_min:.3f}, max: {mask_max:.3f}, mean: {mask_mean:.3f}")

            # Check if negative areas have lower logits
            if negative_count > 0:
                print("Debug: Negative clicks should result in lower mask logits in those areas")

        return out_obj_ids, out_mask_logits

    def add_box_annotation(self, frame_idx, box, obj_id=1):
        """
        Add bounding box annotation to a frame.

        Args:
            frame_idx (int): Frame index (0-based)
            box (list): Bounding box as [x1, y1, x2, y2]
            obj_id (int): Object ID for tracking

        Returns:
            tuple: (out_obj_ids, out_mask_logits) from the predictor
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        box = np.array(box, dtype=np.float32)

        print(f"Adding box annotation to frame {frame_idx}")
        print(f"Box: {box}")
        print(f"Object ID: {obj_id}")

        # Add box to the predictor
        _, out_obj_ids, out_mask_logits = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            box=box,
        )

        return out_obj_ids, out_mask_logits

    def test_negative_click_effectiveness(self, frame_idx, positive_point, negative_point, obj_id=1):
        """
        Test the effectiveness of negative clicks by comparing masks with and without negative clicks.

        Args:
            frame_idx (int): Frame index to test
            positive_point (list): [x, y] coordinates for positive click
            negative_point (list): [x, y] coordinates for negative click
            obj_id (int): Object ID for tracking

        Returns:
            dict: Test results with mask comparisons
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        print(f"\n=== Testing Negative Click Effectiveness ===")
        print(f"Frame: {frame_idx}, Object ID: {obj_id}")
        print(f"Positive click: {positive_point}")
        print(f"Negative click: {negative_point}")

        # Test 1: Only positive click
        print("\n1. Testing with only positive click...")
        _, _, mask_logits_pos_only = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            points=np.array([positive_point], dtype=np.float32),
            labels=np.array([1], dtype=np.int32),
        )

        # Reset state for clean test
        self.reset_state()
        self.init_video_state(self.video_frames_dir)

        # Test 2: Positive + negative clicks
        print("\n2. Testing with positive + negative clicks...")
        _, _, mask_logits_with_neg = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            points=np.array([positive_point, negative_point], dtype=np.float32),
            labels=np.array([1, 0], dtype=np.int32),
        )

        # Compare masks
        if len(mask_logits_pos_only) > 0 and len(mask_logits_with_neg) > 0:
            mask_pos_only = mask_logits_pos_only[0]
            mask_with_neg = mask_logits_with_neg[0]

            # Convert to binary masks
            binary_pos_only = (mask_pos_only > 0.0).cpu().numpy()
            binary_with_neg = (mask_with_neg > 0.0).cpu().numpy()

            # Calculate differences
            total_pixels = binary_pos_only.size
            pixels_removed = np.sum(binary_pos_only & ~binary_with_neg)
            pixels_added = np.sum(~binary_pos_only & binary_with_neg)

            # Check negative click area specifically
            neg_x, neg_y = int(negative_point[0]), int(negative_point[1])
            h, w = binary_pos_only.shape

            # Sample area around negative click (5x5 window)
            window_size = 5
            y_start = max(0, neg_y - window_size//2)
            y_end = min(h, neg_y + window_size//2 + 1)
            x_start = max(0, neg_x - window_size//2)
            x_end = min(w, neg_x + window_size//2 + 1)

            neg_area_before = binary_pos_only[y_start:y_end, x_start:x_end]
            neg_area_after = binary_with_neg[y_start:y_end, x_start:x_end]
            neg_area_reduced = np.sum(neg_area_before) - np.sum(neg_area_after)

            results = {
                'total_pixels': total_pixels,
                'pixels_removed': pixels_removed,
                'pixels_added': pixels_added,
                'negative_area_reduced': neg_area_reduced,
                'negative_click_effective': neg_area_reduced > 0,
                'mask_pos_only': mask_pos_only,
                'mask_with_neg': mask_with_neg
            }

            print(f"\n=== Results ===")
            print(f"Total pixels: {total_pixels}")
            print(f"Pixels removed by negative click: {pixels_removed}")
            print(f"Pixels added by negative click: {pixels_added}")
            print(f"Negative click area reduced: {neg_area_reduced} pixels")
            print(f"Negative click effective: {results['negative_click_effective']}")

            if not results['negative_click_effective']:
                print("\n[WARNING]  WARNING: Negative click appears ineffective!")
                print("Possible causes:")
                print("1. Negative click too close to positive click")
                print("2. Model confidence too high in that area")
                print("3. Need multiple negative clicks")
                print("4. Try different negative click locations")

            return results
        else:
            print("[ERROR] Error: Could not generate masks for comparison")
            return None

    def detect_objects_with_text_prompt(self, video_frames_dir, text_prompt,
                                       box_threshold=0.25, text_threshold=0.25,
                                       frame_step=20, max_frames=None, enable_visualization=True,
                                       batch_propagation=True, max_propagation_frames=50, prompt_frame_idx=0,
                                       use_enhanced_prompts=True):
        """
        Automatically detect and segment objects using text prompts across video frames with performance optimizations.

        Args:
            video_frames_dir (str): Directory containing video frames
            text_prompt (str): Text description of objects to detect (e.g., "person. car. dog.")
            box_threshold (float): Box confidence threshold for Grounding DINO
            text_threshold (float): Text confidence threshold for Grounding DINO
            frame_step (int): Process every N-th frame for detection
            max_frames (int): Maximum number of frames to process (None for all)
            enable_visualization (bool): Whether to create mask overlay visualizations during detection
            batch_propagation (bool): Whether to use optimized batch propagation
            max_propagation_frames (int): Maximum frames to propagate per batch for performance
            prompt_frame_idx (int): Specific frame index to use for Grounded SAM 2 prompting (default: 0)
            use_enhanced_prompts (bool): Whether to use enhanced prompts for better detection (default: True)

        Returns:
            dict: Dictionary mapping frame indices to detection results
        """
        if not self.enable_grounding_dino:
            raise RuntimeError("Grounding DINO not enabled. Initialize with enable_grounding_dino=True")

        if self.grounding_detector is None or self.sam_image_predictor is None:
            raise RuntimeError("Grounding DINO components not loaded")

        print(f"\n=== Starting Text-Based Object Detection ===")
        print(f"Original text prompt: '{text_prompt}'")
        print(f"Enhanced prompts enabled: {use_enhanced_prompts}")
        print(f"Prompt frame index: {prompt_frame_idx}")
        print(f"Frame step: {frame_step}")
        print(f"Box threshold: {box_threshold}")
        print(f"Text threshold: {text_threshold}")

        # Get frame files
        frames_dir = Path(video_frames_dir)
        frame_files = sorted([f for f in frames_dir.glob("*") if f.suffix.lower() in ['.jpg', '.jpeg', '.png']])

        if max_frames:
            frame_files = frame_files[:max_frames]

        print(f"Processing {len(frame_files)} total frames")

        # Validate prompt frame index
        if prompt_frame_idx >= len(frame_files):
            print(f"Warning: prompt_frame_idx ({prompt_frame_idx}) >= total frames ({len(frame_files)}). Using frame 0.")
            prompt_frame_idx = 0

        # Initialize video state
        self.init_video_state(video_frames_dir)

        detection_results = {}
        objects_count = 0

        # Use the specific frame for Grounded SAM 2 prompting (following reference implementation pattern)
        prompt_frame_file = frame_files[prompt_frame_idx]
        print(f"\n--- Using frame {prompt_frame_idx} for Grounded SAM 2 prompting: {prompt_frame_file.name} ---")

        # Load the prompt frame image
        prompt_image = Image.open(prompt_frame_file).convert("RGB")

        # Detect objects with Grounding DINO on the prompt frame (with enhanced prompts)
        detection_result = self.grounding_detector.detect_objects(
            prompt_image, text_prompt, box_threshold, text_threshold, use_enhanced_prompts
        )

        if len(detection_result['boxes']) == 0:
            print(f"No objects detected in prompt frame {prompt_frame_idx}")
            return {}

        # Generate masks using SAM image predictor on the prompt frame
        self.sam_image_predictor.set_image(np.array(prompt_image))

        masks, scores, logits = self.sam_image_predictor.predict(
            point_coords=None,
            point_labels=None,
            box=detection_result['boxes'],
            multimask_output=False,
        )

        # Convert mask shape to (n, H, W) if needed
        if masks.ndim == 2:
            masks = masks[None]
        elif masks.ndim == 4:
            masks = masks.squeeze(1)

        # Register masks with video predictor using the prompt frame
        frame_object_ids = []
        for i, (mask, box, label) in enumerate(zip(masks, detection_result['boxes'], detection_result['labels'])):
            objects_count += 1
            object_id = objects_count

            print(f"  Registering object {object_id}: {label}")

            # Add mask to video predictor using the prompt frame index
            _, out_obj_ids, out_mask_logits = self.predictor.add_new_mask(
                self.inference_state,
                prompt_frame_idx,
                object_id,
                torch.tensor(mask).to(self.device),
            )

            frame_object_ids.append(object_id)

        detection_results[prompt_frame_idx] = {
            'boxes': detection_result['boxes'],
            'labels': detection_result['labels'],
            'masks': masks,
            'object_ids': frame_object_ids,
            'scores': detection_result.get('scores', [])
        }

        print(f"  Registered {len(frame_object_ids)} objects for tracking")

        # Real-time mask overlay visualization
        if enable_visualization:
            self._create_detection_overlay(
                prompt_image, masks, detection_result['boxes'],
                detection_result['labels'], prompt_frame_file.name
            )

        print(f"\n[OK] Text-based detection completed!")
        print(f"Total objects detected: {objects_count}")
        print(f"Frames with detections: {len([r for r in detection_results.values() if len(r['object_ids']) > 0])}")

        # Store detection results for potential restoration during reset
        self._last_detection_results = detection_results.copy()

        return detection_results

    def reset_state(self, preserve_annotations=False):
        """
        Reset the video state to allow new object IDs.

        Args:
            preserve_annotations (bool): If True, preserve existing annotations for manual refinement
        """
        stored_annotations = {}

        if preserve_annotations:
            print("Resetting video state while preserving annotations for manual refinement...")
            # Store current state for restoration
            stored_annotations = self._store_annotations()
        else:
            print("Resetting video state to allow new object IDs...")

        # Reset the inference state
        self.inference_state = None

        if hasattr(self, 'video_frames_dir') and self.video_frames_dir:
            try:
                self.init_video_state(self.video_frames_dir)

                # Restore annotations if preserving
                if preserve_annotations and stored_annotations:
                    print("Restoring preserved annotations...")
                    self._restore_annotations(stored_annotations)
                    print(f"[OK] Restored {len(stored_annotations)} detection frames")

                print("Video state reset successfully.")
            except Exception as e:
                print(f"Warning: Failed to reinitialize video state: {e}")
                print("You will need to call init_video_state() manually with a valid video path.")
        else:
            print("Warning: No video frames directory stored. Call init_video_state() manually.")

    def _store_annotations(self):
        """Store current annotations for preservation during reset."""
        stored_data = {}

        # Store detection results if they exist
        if hasattr(self, '_last_detection_results') and self._last_detection_results:
            print(f"Storing {len(self._last_detection_results)} detection frames for preservation...")
            stored_data = self._last_detection_results.copy()

        return stored_data

    def _restore_annotations(self, stored_annotations):
        """Restore previously stored annotations."""
        if not stored_annotations:
            print("No annotations to restore.")
            return

        print(f"Restoring {len(stored_annotations)} detection frames...")

        # Create new detection results with updated object IDs
        restored_detection_results = {}
        current_object_id = 1

        # Restore each detection frame
        for frame_idx, detection_data in stored_annotations.items():
            if not detection_data.get('object_ids'):
                continue

            print(f"  Restoring frame {frame_idx}: {len(detection_data['object_ids'])} objects")

            # Prepare restored frame data
            restored_frame_data = {
                'boxes': detection_data.get('boxes', []),
                'labels': detection_data.get('labels', []),
                'masks': [],
                'object_ids': [],
                'scores': detection_data.get('scores', [])
            }

            # Re-register each object with the video predictor using new object IDs
            for i, (mask, original_object_id, label) in enumerate(zip(
                detection_data['masks'],
                detection_data['object_ids'],
                detection_data['labels']
            )):
                try:
                    # Use new sequential object ID
                    new_object_id = current_object_id
                    current_object_id += 1

                    # Ensure mask is in correct format
                    if isinstance(mask, np.ndarray):
                        mask_tensor = torch.tensor(mask).to(self.device)
                    else:
                        mask_tensor = torch.tensor(np.array(mask)).to(self.device)

                    # Add mask back to video predictor with new object ID
                    _, out_obj_ids, out_mask_logits = self.predictor.add_new_mask(
                        self.inference_state,
                        frame_idx,
                        new_object_id,
                        mask_tensor,
                    )

                    # Store the restored data with new object ID
                    restored_frame_data['masks'].append(mask)
                    restored_frame_data['object_ids'].append(new_object_id)

                    print(f"    Restored object {original_object_id} → {new_object_id}: {label}")
                except Exception as e:
                    print(f"    Warning: Failed to restore object {original_object_id}: {e}")

            # Store the restored frame data
            if restored_frame_data['object_ids']:
                restored_detection_results[frame_idx] = restored_frame_data

        # Store the restored detection results with updated object IDs
        self._last_detection_results = restored_detection_results
        print(f"[OK] All annotations restored successfully! Total objects: {current_object_id - 1}")

    def propagate_masks(self, max_frame_num_to_track=None, start_frame_idx=0, reverse=False, batch_size=50):
        """
        Propagate masks throughout the video with optimized performance.

        Uses efficient implementation similar to grounded_sam2_tracking_demo.py
        with minimal overhead and direct mask processing.

        Args:
            max_frame_num_to_track (int): Maximum number of frames to track from start_frame_idx (None for all)
            start_frame_idx (int): Starting frame index for propagation
            reverse (bool): Whether to propagate in reverse direction
            batch_size (int): Number of frames to process in each batch for memory efficiency

        Returns:
            dict: Dictionary mapping frame indices to segmentation masks
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        import time
        start_time = time.time()

        print("🚀 Starting optimized mask propagation...")
        if max_frame_num_to_track:
            print(f"  [STATS] Limited propagation: {max_frame_num_to_track} frames from frame {start_frame_idx}")
        if reverse:
            print("  ⏪ Reverse propagation enabled")

        # Use efficient propagation similar to reference implementation
        video_segments = {}

        try:
            # Prepare propagation parameters (minimal overhead)
            propagation_kwargs = {'inference_state': self.inference_state}

            if max_frame_num_to_track is not None:
                propagation_kwargs['max_frame_num_to_track'] = max_frame_num_to_track
            if start_frame_idx != 0:
                propagation_kwargs['start_frame_idx'] = start_frame_idx
            if reverse:
                propagation_kwargs['reverse'] = reverse

            # Optimized propagation loop with minimal processing overhead
            frame_count = 0
            batch_count = 0
            last_batch_time = time.time()

            # Direct propagation without unnecessary intermediate storage
            for out_frame_idx, out_obj_ids, out_mask_logits in self.predictor.propagate_in_video(**propagation_kwargs):
                # Efficient mask processing - convert to binary masks immediately
                frame_masks = {}
                for i, out_obj_id in enumerate(out_obj_ids):
                    # Direct conversion to binary mask (similar to reference implementation)
                    frame_masks[out_obj_id] = (out_mask_logits[i] > 0.0).cpu().numpy()

                video_segments[out_frame_idx] = frame_masks
                frame_count += 1

                # Optimized batch processing with performance metrics
                if frame_count % batch_size == 0:
                    batch_count += 1
                    current_time = time.time()
                    batch_time = current_time - last_batch_time
                    fps = batch_size / batch_time if batch_time > 0 else 0

                    print(f"  [SPEED] Batch {batch_count}: {frame_count} frames | {fps:.1f} FPS | {batch_time:.2f}s")

                    # Efficient memory management
                    if self.device.type == "cuda":
                        torch.cuda.empty_cache()

                    last_batch_time = current_time

        except Exception as e:
            print(f"[ERROR] Error during optimized propagation: {e}")
            print("[RESET] Falling back to basic propagation...")

            # Fallback with same efficient processing
            for out_frame_idx, out_obj_ids, out_mask_logits in self.predictor.propagate_in_video(self.inference_state):
                frame_masks = {}
                for i, out_obj_id in enumerate(out_obj_ids):
                    frame_masks[out_obj_id] = (out_mask_logits[i] > 0.0).cpu().numpy()
                video_segments[out_frame_idx] = frame_masks

        # Performance summary
        total_time = time.time() - start_time
        avg_fps = len(video_segments) / total_time if total_time > 0 else 0

        print(f"[OK] Propagation completed!")
        print(f"  [STATS] Processed {len(video_segments)} frames in {total_time:.2f}s")
        print(f"  [SPEED] Average speed: {avg_fps:.1f} FPS")
        print(f"  [MEMORY] Memory optimized with {batch_count} batches")

        # Store for potential reuse
        self._last_video_segments = video_segments
        return video_segments

    def _create_detection_overlay(self, image, masks, boxes, labels, frame_name):
        """
        Create real-time mask overlay visualization during detection.

        Args:
            image (PIL.Image): Original frame image
            masks (np.ndarray): Detection masks
            boxes (list): Bounding boxes
            labels (list): Object labels
            frame_name (str): Frame filename for display
        """
        try:
            import matplotlib.pyplot as plt
            import matplotlib.patches as patches
            import matplotlib.colors as mcolors

            # Use non-interactive backend for real-time processing
            plt.ioff()

            fig, ax = plt.subplots(1, 1, figsize=(12, 8))
            ax.imshow(np.array(image))
            ax.set_title(f"Real-time Detection: {frame_name}")

            # Create colormap for masks
            colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'cyan', 'magenta']

            # Overlay masks
            for i, (mask, box, label) in enumerate(zip(masks, boxes, labels)):
                color = colors[i % len(colors)]

                # Convert mask to proper format for visualization
                try:
                    # Handle different mask types (tensor, numpy array, etc.)
                    if hasattr(mask, 'cpu'):  # PyTorch tensor (possibly on CUDA)
                        mask_np = mask.cpu().numpy()
                    elif hasattr(mask, 'numpy'):  # Other tensor types
                        mask_np = mask.numpy()
                    else:
                        mask_np = np.array(mask)  # Already numpy or list

                    # Ensure mask is boolean for proper indexing
                    if mask_np.dtype != bool:
                        mask_bool = mask_np > 0.5  # Convert to boolean using threshold
                    else:
                        mask_bool = mask_np

                    # Show mask overlay with transparency
                    mask_overlay = np.zeros((*mask_bool.shape, 4))
                    rgba_color = mcolors.to_rgba(color, alpha=0.3)
                    mask_overlay[mask_bool] = rgba_color
                    ax.imshow(mask_overlay)

                except Exception as mask_error:
                    print(f"      Warning: Could not process mask {i}: {mask_error}")
                    continue

                # Convert box to proper format for visualization
                try:
                    # Handle different box types (tensor, numpy array, list)
                    if hasattr(box, 'cpu'):  # PyTorch tensor (possibly on CUDA)
                        box_np = box.cpu().numpy()
                    elif hasattr(box, 'numpy'):  # Other tensor types
                        box_np = box.numpy()
                    else:
                        box_np = np.array(box)  # Already numpy or list

                    # Draw bounding box
                    rect = patches.Rectangle(
                        (box_np[0], box_np[1]), box_np[2] - box_np[0], box_np[3] - box_np[1],
                        linewidth=2, edgecolor=color, facecolor='none'
                    )
                    ax.add_patch(rect)

                    # Add label
                    ax.text(box_np[0], box_np[1] - 5, f"{label} ({i+1})",
                           color=color, fontsize=10, fontweight='bold',
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

                except Exception as box_error:
                    print(f"      Warning: Could not process box {i}: {box_error}")
                    continue

            ax.axis('off')

            # Display briefly for real-time feedback
            plt.show(block=False)
            plt.pause(0.5)  # Show for 0.5 seconds
            plt.close(fig)

            print(f"    [OK] Real-time overlay displayed for {frame_name}")

        except Exception as e:
            print(f"    [WARNING]  Warning: Could not create real-time overlay: {e}")

    def visualize_frame_with_masks(self, frame_path, masks_data, output_path=None, show_plot=True):
        """
        Visualize a frame with overlaid masks.

        Args:
            frame_path (str): Path to the frame image
            masks_data (dict): Dictionary containing 'obj_ids' and 'mask_logits'
            output_path (str): Path to save the visualization (optional)
            show_plot (bool): Whether to display the plot

        Returns:
            matplotlib.figure.Figure: The created figure
        """
        # Load the frame
        frame = Image.open(frame_path)
        frame_array = np.array(frame)

        # Create figure with non-interactive backend if not showing
        if not show_plot:
            # Use Agg backend to prevent display
            import matplotlib
            current_backend = matplotlib.get_backend()
            matplotlib.use('Agg')

        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(frame_array)
        ax.set_title(f"Frame: {Path(frame_path).name}")

        # Overlay masks (handle both old and new formats)
        if masks_data:
            if 'mask_logits' in masks_data:
                # Old format: {obj_ids: [...], mask_logits: [...]}
                obj_ids = masks_data['obj_ids']
                mask_logits = masks_data['mask_logits']

                for i, obj_id in enumerate(obj_ids):
                    mask = (mask_logits[i] > 0.0).cpu().numpy()
                    self._show_mask(mask, ax, obj_id=obj_id)
            else:
                # New format: {obj_id: mask_array, ...}
                for obj_id, mask_array in masks_data.items():
                    # Handle different mask formats
                    if hasattr(mask_array, 'cpu'):  # PyTorch tensor
                        mask = mask_array.cpu().numpy().squeeze()
                    elif hasattr(mask_array, 'squeeze'):  # NumPy array
                        mask = mask_array.squeeze()
                    else:
                        mask = np.array(mask_array).squeeze()

                    # Ensure mask is 2D
                    if mask.ndim > 2:
                        mask = mask[0] if mask.shape[0] == 1 else mask.squeeze()

                    # Convert to boolean mask if needed
                    if mask.dtype != bool:
                        mask = mask > 0.0

                    self._show_mask(mask, ax, obj_id=obj_id)

        ax.axis('off')

        # Save if output path provided
        if output_path:
            plt.savefig(output_path, bbox_inches='tight', dpi=150)
            print(f"Visualization saved to: {output_path}")

        # Show plot if requested
        if show_plot:
            plt.show()
        else:
            # Restore original backend if we changed it
            import matplotlib
            if 'current_backend' in locals():
                matplotlib.use(current_backend)

        return fig

    def _show_mask(self, mask, ax, obj_id=None, random_color=False):
        """
        Display mask overlay on the given axes.

        Args:
            mask (np.ndarray): Binary mask array
            ax (matplotlib.axes.Axes): Matplotlib axes to draw on
            obj_id (int): Object ID for color selection
            random_color (bool): Whether to use random colors
        """
        if random_color:
            color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
        else:
            # Use a more diverse color palette and ensure unique colors per object ID
            colors = [
                [1.0, 0.0, 0.0],  # Red
                [0.0, 1.0, 0.0],  # Green
                [0.0, 0.0, 1.0],  # Blue
                [1.0, 1.0, 0.0],  # Yellow
                [1.0, 0.0, 1.0],  # Magenta
                [0.0, 1.0, 1.0],  # Cyan
                [1.0, 0.5, 0.0],  # Orange
                [0.5, 0.0, 1.0],  # Purple
                [0.0, 0.5, 0.0],  # Dark Green
                [0.5, 0.5, 0.5],  # Gray
            ]

            if obj_id is None:
                color_rgb = colors[0]
            else:
                # Use modulo to cycle through colors, but ensure different objects get different colors
                color_idx = (obj_id - 1) % len(colors)
                color_rgb = colors[color_idx]

            color = np.array([*color_rgb, 0.6])  # Add alpha

        h, w = mask.shape[-2:]
        mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
        ax.imshow(mask_image)

    def _show_points(self, coords, labels, ax, marker_size=200):
        """
        Display point annotations on the given axes.

        Args:
            coords (np.ndarray): Point coordinates
            labels (np.ndarray): Point labels (1 for positive, 0 for negative)
            ax (matplotlib.axes.Axes): Matplotlib axes to draw on
            marker_size (int): Size of the markers
        """
        pos_points = coords[labels == 1]
        neg_points = coords[labels == 0]
        ax.scatter(pos_points[:, 0], pos_points[:, 1], color='green', marker='*',
                  s=marker_size, edgecolor='white', linewidth=1.25)
        ax.scatter(neg_points[:, 0], neg_points[:, 1], color='red', marker='*',
                  s=marker_size, edgecolor='white', linewidth=1.25)

    def _show_box(self, box, ax):
        """
        Display bounding box on the given axes.

        Args:
            box (np.ndarray): Bounding box as [x1, y1, x2, y2]
            ax (matplotlib.axes.Axes): Matplotlib axes to draw on
        """
        x0, y0 = box[0], box[1]
        w, h = box[2] - box[0], box[3] - box[1]
        ax.add_patch(plt.Rectangle((x0, y0), w, h, edgecolor='green',
                                 facecolor=(0, 0, 0, 0), lw=2))

    def export_masks(self, video_segments, output_dir):
        """
        Export masks as PNG files.

        Args:
            video_segments (dict): Video segments from propagate_masks()
            output_dir (str): Directory to save mask files
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        print(f"Exporting masks to: {output_dir}")

        for frame_idx, masks_data in tqdm(video_segments.items(), desc="Exporting masks"):
            if 'mask_logits' in masks_data:
                obj_ids = masks_data['obj_ids']
                mask_logits = masks_data['mask_logits']

                for i, obj_id in enumerate(obj_ids):
                    mask = (mask_logits[i] > 0.0).cpu().numpy().squeeze()

                    # Convert to uint8
                    mask_uint8 = (mask * 255).astype(np.uint8)

                    # Save mask
                    mask_filename = f"frame_{frame_idx:05d}_obj_{obj_id:02d}_mask.png"
                    mask_path = output_dir / mask_filename

                    Image.fromarray(mask_uint8).save(mask_path)

        print(f"Masks exported successfully!")

    def export_masks_with_overlay_fast(self, video_segments, frames_dir, output_dir):
        """
        Export masks with overlay using supervision library for fast performance.

        Args:
            video_segments (dict): Video segments from propagate_masks()
            frames_dir (str): Directory containing original frames
            output_dir (str): Directory to save overlaid frames
        """
        if not SUPERVISION_AVAILABLE:
            print("[WARN] Supervision not available, falling back to matplotlib method")
            return self.export_masks_with_overlay_matplotlib(video_segments, frames_dir, output_dir)

        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        print(f"Exporting masks with fast overlay to: {output_dir}")

        # Get frame files
        frames_dir = Path(frames_dir)
        frame_files = sorted([f for f in frames_dir.glob("*") if f.suffix.lower() in ['.jpg', '.jpeg', '.png']])

        for frame_idx, masks_data in tqdm(video_segments.items(), desc="Creating overlay frames"):
            if frame_idx < len(frame_files):
                frame_path = frame_files[frame_idx]
                output_path = output_dir / f"overlay_{frame_path.name}"

                # Load image with OpenCV (faster than PIL for this use case)
                image = cv2.imread(str(frame_path))
                if image is None:
                    print(f"[WARN] Could not load image: {frame_path}")
                    continue

                # Process masks if available (video_segments format: {obj_id: mask_array})
                if masks_data and len(masks_data) > 0:
                    # Convert masks to supervision format
                    masks = []
                    boxes = []
                    class_ids = []

                    for obj_id, mask_array in masks_data.items():
                        # Handle different mask formats
                        if hasattr(mask_array, 'cpu'):  # PyTorch tensor
                            mask = mask_array.cpu().numpy().squeeze()
                        elif hasattr(mask_array, 'squeeze'):  # NumPy array
                            mask = mask_array.squeeze()
                        else:
                            mask = np.array(mask_array).squeeze()

                        # Ensure mask is 2D
                        if mask.ndim > 2:
                            mask = mask[0] if mask.shape[0] == 1 else mask.squeeze()

                        # Convert to boolean mask
                        if mask.dtype != bool:
                            mask = mask > 0.0

                        masks.append(mask)
                        class_ids.append(obj_id)

                        # Calculate bounding box from mask
                        rows = np.any(mask, axis=1)
                        cols = np.any(mask, axis=0)
                        if rows.any() and cols.any():
                            rmin, rmax = np.where(rows)[0][[0, -1]]
                            cmin, cmax = np.where(cols)[0][[0, -1]]
                            boxes.append([cmin, rmin, cmax, rmax])
                        else:
                            boxes.append([0, 0, 0, 0])

                    if len(masks) > 0:
                        # Create supervision detections
                        masks_array = np.array(masks)
                        boxes_array = np.array(boxes)
                        class_ids_array = np.array(class_ids, dtype=np.int32)

                        detections = sv.Detections(
                            xyxy=boxes_array,
                            mask=masks_array.astype(bool),
                            class_id=class_ids_array,
                        )

                        # Create labels
                        labels = [f"Object {obj_id}" for obj_id in class_ids]

                        # Apply annotations using supervision (much faster than matplotlib)
                        box_annotator = sv.BoxAnnotator()
                        annotated_frame = box_annotator.annotate(scene=image.copy(), detections=detections)

                        label_annotator = sv.LabelAnnotator()
                        annotated_frame = label_annotator.annotate(annotated_frame, detections=detections, labels=labels)

                        mask_annotator = sv.MaskAnnotator()
                        annotated_frame = mask_annotator.annotate(scene=annotated_frame, detections=detections)

                        # Save annotated frame
                        cv2.imwrite(str(output_path), annotated_frame)
                    else:
                        # No valid masks, just copy original image
                        cv2.imwrite(str(output_path), image)
                else:
                    # No mask data, just copy original image
                    cv2.imwrite(str(output_path), image)

        print(f"Fast overlay frames exported successfully!")
        return str(output_dir)

    def export_masks_with_overlay_matplotlib(self, video_segments, frames_dir, output_dir):
        """
        Export masks with overlay using matplotlib (slower but more compatible).

        Args:
            video_segments (dict): Video segments from propagate_masks()
            frames_dir (str): Directory containing original frames
            output_dir (str): Directory to save overlaid frames
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        print(f"Exporting masks with matplotlib overlay to: {output_dir}")

        # Get frame files
        frames_dir = Path(frames_dir)
        frame_files = sorted([f for f in frames_dir.glob("*") if f.suffix.lower() in ['.jpg', '.jpeg', '.png']])

        # Disable interactive mode to prevent popup windows
        plt.ioff()

        for frame_idx, masks_data in tqdm(video_segments.items(), desc="Creating overlay frames"):
            if frame_idx < len(frame_files):
                frame_path = frame_files[frame_idx]
                output_path = output_dir / f"overlay_{frame_path.name}"

                # Create visualization without showing and ensure no display
                fig = self.visualize_frame_with_masks(
                    str(frame_path),
                    masks_data,
                    output_path=str(output_path),
                    show_plot=False
                )

                # Explicitly close the figure to prevent memory leaks and display
                if fig:
                    plt.close(fig)
                plt.close('all')  # Close any remaining figures

        # Re-enable interactive mode for future use
        plt.ion()

        print(f"Matplotlib overlay frames exported successfully!")
        return str(output_dir)

    def export_masks_with_overlay(self, video_segments, frames_dir, output_dir):
        """
        Export masks with overlay on original frames (automatically chooses fastest method).

        Args:
            video_segments (dict): Video segments from propagate_masks()
            frames_dir (str): Directory containing original frames
            output_dir (str): Directory to save overlaid frames
        """
        # Use fast supervision method if available, otherwise fall back to matplotlib
        if SUPERVISION_AVAILABLE:
            return self.export_masks_with_overlay_fast(video_segments, frames_dir, output_dir)
        else:
            return self.export_masks_with_overlay_matplotlib(video_segments, frames_dir, output_dir)

    def save_masks_and_json(self, video_segments, frames_dir, mask_data_dir, json_data_dir):
        """
        Save masks and JSON metadata following Grounded-SAM-2 format.

        Args:
            video_segments (dict): Video segments from propagate_masks()
            frames_dir (str): Directory containing original frames
            mask_data_dir (str): Directory to save mask files (.npy)
            json_data_dir (str): Directory to save JSON metadata files
        """
        print(f"Saving masks and JSON data...")
        print(f"Mask data directory: {mask_data_dir}")
        print(f"JSON data directory: {json_data_dir}")

        # Get frame files
        frames_dir = Path(frames_dir)
        frame_files = sorted([f for f in frames_dir.glob("*") if f.suffix.lower() in ['.jpg', '.jpeg', '.png']])

        # Get frame dimensions from first frame
        if frame_files:
            first_frame = Image.open(frame_files[0])
            frame_width, frame_height = first_frame.size
        else:
            frame_width, frame_height = 1920, 1080

        saved_count = 0

        for frame_idx, masks_data in tqdm(video_segments.items(), desc="Saving masks and JSON"):
            if frame_idx < len(frame_files):
                frame_file = frame_files[frame_idx]
                image_base_name = frame_file.stem

                # Create mask array
                mask_img = np.zeros((frame_height, frame_width), dtype=np.uint16)

                # Create mask dictionary model
                mask_dict = MaskDictionaryModel(
                    mask_name=f"mask_{image_base_name}.npy",
                    mask_height=frame_height,
                    mask_width=frame_width,
                    promote_type="mask"
                )

                if 'mask_logits' in masks_data and 'obj_ids' in masks_data:
                    obj_ids = masks_data['obj_ids']
                    mask_logits = masks_data['mask_logits']

                    for i, obj_id in enumerate(obj_ids):
                        # Convert mask logits to binary mask
                        mask = (mask_logits[i] > 0.0).cpu().numpy().squeeze()

                        # Add to combined mask image
                        mask_img[mask] = obj_id

                        # Create object info
                        obj_info = ObjectInfo(
                            mask=mask,
                            object_id=obj_id,
                            confidence=1.0
                        )
                        obj_info.update_box()
                        mask_dict.labels[obj_id] = obj_info

                # Save mask file (.npy)
                mask_file_path = os.path.join(mask_data_dir, f"mask_{image_base_name}.npy")
                np.save(mask_file_path, mask_img)

                # Save JSON file
                json_file_path = os.path.join(json_data_dir, f"mask_{image_base_name}.json")
                mask_dict.to_json(json_file_path)

                saved_count += 1

        print(f"[OK] Saved {saved_count} mask and JSON file pairs")
        print(f"Mask files saved to: {mask_data_dir}")
        print(f"JSON files saved to: {json_data_dir}")

    def save_png_masks(self, video_segments, frames_dir, mask_data_dir):
        """
        Save PNG mask visualization files alongside existing .npy files.

        Args:
            video_segments (dict): Video segments from propagate_masks()
            frames_dir (str): Directory containing original frames
            mask_data_dir (str): Directory to save PNG mask files
        """
        print(f"Saving PNG mask visualization files...")

        # Create PNG subdirectory
        png_mask_dir = Path(mask_data_dir) / "png_masks"
        png_mask_dir.mkdir(parents=True, exist_ok=True)

        # Get frame files
        frames_dir = Path(frames_dir)
        frame_files = sorted([f for f in frames_dir.glob("*") if f.suffix.lower() in ['.jpg', '.jpeg', '.png']])

        saved_count = 0

        for frame_idx, masks_data in tqdm(video_segments.items(), desc="Saving PNG masks"):
            if frame_idx < len(frame_files):
                frame_file = frame_files[frame_idx]
                image_base_name = frame_file.stem

                # Create combined mask for all objects in this frame
                if masks_data:
                    # Get frame dimensions from first mask (handle 3D masks)
                    first_mask = next(iter(masks_data.values()))

                    # Handle different mask dimensions
                    if first_mask.ndim > 2:
                        # If mask is 3D, try different strategies to get 2D
                        if first_mask.shape[0] == 1:
                            # Shape like (1, H, W) - remove batch dimension
                            first_mask_2d = first_mask[0]
                        elif first_mask.shape[-1] == 1:
                            # Shape like (H, W, 1) - remove channel dimension
                            first_mask_2d = first_mask.squeeze(-1)
                        else:
                            # Try general squeeze, or take first slice if multiple batches
                            squeezed = first_mask.squeeze()
                            if squeezed.ndim == 2:
                                first_mask_2d = squeezed
                            else:
                                # Last resort: take first slice along first dimension
                                first_mask_2d = first_mask[0]
                    else:
                        first_mask_2d = first_mask

                    # Ensure we have 2D mask for shape extraction
                    if first_mask_2d.ndim != 2:
                        print(f"Warning: Could not convert mask to 2D. Shape: {first_mask_2d.shape}")
                        continue

                    h, w = first_mask_2d.shape
                    combined_mask = np.zeros((h, w), dtype=np.uint16)

                    # Combine all object masks with unique IDs
                    for obj_id, mask in masks_data.items():
                        # Handle 3D masks consistently using the same logic as above
                        if mask.ndim > 2:
                            if mask.shape[0] == 1:
                                # Shape like (1, H, W) - remove batch dimension
                                mask_2d = mask[0]
                            elif mask.shape[-1] == 1:
                                # Shape like (H, W, 1) - remove channel dimension
                                mask_2d = mask.squeeze(-1)
                            else:
                                # Try general squeeze, or take first slice if multiple batches
                                squeezed = mask.squeeze()
                                if squeezed.ndim == 2:
                                    mask_2d = squeezed
                                else:
                                    # Last resort: take first slice along first dimension
                                    mask_2d = mask[0]
                        else:
                            mask_2d = mask

                        # Ensure mask is 2D before processing
                        if mask_2d.ndim == 2:
                            combined_mask[mask_2d > 0] = obj_id
                        else:
                            print(f"Warning: Skipping object {obj_id} - could not convert to 2D mask")

                    # Save PNG mask with object IDs as pixel values
                    png_filename = f"mask_{image_base_name}.png"
                    png_path = png_mask_dir / png_filename

                    # Convert to 8-bit for PNG (scale object IDs to visible range)
                    if combined_mask.max() > 0:
                        # Scale to 0-255 range for visualization
                        mask_normalized = (combined_mask * 255 // max(combined_mask.max(), 1)).astype(np.uint8)
                    else:
                        mask_normalized = combined_mask.astype(np.uint8)

                    Image.fromarray(mask_normalized).save(png_path)
                    saved_count += 1

        print(f"[OK] Saved {saved_count} PNG mask files to: {png_mask_dir}")


    def clean_output_directory(self, output_dir):
        """
        Clean the output directory to prevent mixing results from different runs.

        Args:
            output_dir (str): Directory to clean
        """
        output_path = Path(output_dir)

        if output_path.exists():
            print(f"Cleaning existing output directory: {output_path}")

            # Remove masks directory
            masks_dir = output_path / "masks"
            if masks_dir.exists():
                shutil.rmtree(masks_dir)
                print(f"  - Removed masks directory")

            # Remove visualizations directory
            viz_dir = output_path / "visualizations"
            if viz_dir.exists():
                shutil.rmtree(viz_dir)
                print(f"  - Removed visualizations directory")

            # Remove any other files in the output directory
            for item in output_path.iterdir():
                if item.is_file():
                    item.unlink()
                    print(f"  - Removed file: {item.name}")
                elif item.is_dir():
                    shutil.rmtree(item)
                    print(f"  - Removed directory: {item.name}")

        print("Output directory cleaned successfully!")

    def display_frame_list(self, frame_files):
        """
        Display a list of available frames for user selection.

        Args:
            frame_files (list): List of frame file paths

        Returns:
            None
        """
        print("\n" + "=" * 60)
        print("Available Frames for Annotation")
        print("=" * 60)

        # Display frames in groups of 10 for better readability
        for i in range(0, len(frame_files), 10):
            end_idx = min(i + 10, len(frame_files))
            frame_group = frame_files[i:end_idx]

            print(f"\nFrames {i}-{end_idx-1}:")
            for j, frame_file in enumerate(frame_group):
                frame_idx = i + j
                frame_name = frame_file.name
                print(f"  [{frame_idx:3d}] {frame_name}")

        print(f"\nTotal frames available: {len(frame_files)}")
        print("=" * 60)

    def validate_coordinates(self, coords, frame_width, frame_height):
        """
        Validate that coordinates are within frame boundaries.

        Args:
            coords (list): List of coordinates to validate
            frame_width (int): Frame width
            frame_height (int): Frame height

        Returns:
            bool: True if all coordinates are valid
        """
        for coord in coords:
            x, y = coord[:2]  # Take first two elements (x, y)
            if x < 0 or x >= frame_width or y < 0 or y >= frame_height:
                print(f"Error: Coordinate ({x}, {y}) is outside frame boundaries")
                print(f"Frame size: {frame_width} x {frame_height}")
                return False
        return True

    def display_frame_with_annotations(self, frame_path, points=None, labels=None, boxes=None):
        """
        Display a frame with current annotations overlaid.

        Args:
            frame_path (str): Path to the frame image
            points (list): List of point coordinates
            labels (list): List of point labels
            boxes (list): List of bounding boxes

        Returns:
            tuple: (frame_width, frame_height)
        """
        # Load and display the frame
        frame = Image.open(frame_path)
        frame_array = np.array(frame)
        frame_width, frame_height = frame.size

        plt.figure(figsize=(12, 8))
        plt.imshow(frame_array)
        plt.title(f"Frame: {Path(frame_path).name} (Size: {frame_width}x{frame_height})")

        # Overlay existing annotations
        if points and labels:
            points_array = np.array(points)
            labels_array = np.array(labels)
            self._show_points(points_array, labels_array, plt.gca())

        if boxes:
            for box in boxes:
                self._show_box(np.array(box), plt.gca())

        plt.axis('off')
        plt.tight_layout()
        plt.show()

        return frame_width, frame_height


class AnnotationReviewSystem:
    """Comprehensive annotation review and validation system."""

    def __init__(self, predictor, dir_manager):
        """
        Initialize the review system.

        Args:
            predictor: SAM2VideoPredictor instance
            dir_manager: DirectoryManager instance
        """
        self.predictor = predictor
        self.dir_manager = dir_manager
        self.current_review_frame = 0

    def generate_annotation_summary(self, frame_annotations, frame_files):
        """
        Generate comprehensive annotation summary statistics.

        Args:
            frame_annotations (dict): Dictionary of frame annotations
            frame_files (list): List of frame file paths

        Returns:
            dict: Summary statistics
        """
        if not frame_annotations:
            return {
                'total_frames': len(frame_files),
                'annotated_frames': 0,
                'total_points': 0,
                'total_boxes': 0,
                'unique_objects': set(),
                'annotation_density': 0.0,
                'frames_with_points': 0,
                'frames_with_boxes': 0,
                'positive_clicks': 0,
                'negative_clicks': 0
            }

        total_points = 0
        total_boxes = 0
        unique_objects = set()
        frames_with_points = 0
        frames_with_boxes = 0
        positive_clicks = 0
        negative_clicks = 0

        for frame_idx, annotations in frame_annotations.items():
            # Count points
            if 'points' in annotations and annotations['points']:
                total_points += len(annotations['points'])
                frames_with_points += 1

                # Count positive/negative clicks
                for label in annotations.get('labels', []):
                    if label == 1:
                        positive_clicks += 1
                    else:
                        negative_clicks += 1

            # Count boxes (with tensor safety)
            if 'boxes' in annotations:
                boxes = annotations['boxes']
                try:
                    # Handle tensor/array cases safely
                    if hasattr(boxes, 'cpu'):  # PyTorch tensor
                        boxes = boxes.cpu().numpy()

                    # Check if boxes exist and count them
                    if hasattr(boxes, '__len__') and len(boxes) > 0:
                        total_boxes += len(boxes)
                        frames_with_boxes += 1
                except Exception as e:
                    print(f"Warning: Error processing boxes for annotation summary: {e}")
                    # Fallback: check if obj_ids exist as indicator of boxes
                    if 'obj_ids' in annotations and annotations['obj_ids']:
                        total_boxes += len(annotations['obj_ids'])
                        frames_with_boxes += 1

            # Collect unique object IDs
            if 'obj_ids' in annotations:
                unique_objects.update(annotations['obj_ids'])

        annotation_density = len(frame_annotations) / len(frame_files) * 100

        return {
            'total_frames': len(frame_files),
            'annotated_frames': len(frame_annotations),
            'total_points': total_points,
            'total_boxes': total_boxes,
            'unique_objects': unique_objects,
            'annotation_density': annotation_density,
            'frames_with_points': frames_with_points,
            'frames_with_boxes': frames_with_boxes,
            'positive_clicks': positive_clicks,
            'negative_clicks': negative_clicks
        }

    def display_annotation_summary(self, frame_annotations, frame_files):
        """
        Display enhanced visual annotation summary with clear object mapping.

        Provides comprehensive view similar to screenshot format with:
        - Frame-by-frame annotation breakdown
        - Object ID mapping to annotation points
        - Visual representation of annotation data structure
        - Clear tracking of positive/negative clicks per object
        """
        print("\n" + "=" * 80)
        print("[INFO] ENHANCED ANNOTATION SUMMARY")
        print("=" * 80)

        if not frame_annotations:
            print("[ERROR] No annotations found.")
            print(f"Total frames available: {len(frame_files)}")
            print("=" * 80)
            return

        summary = self.generate_annotation_summary(frame_annotations, frame_files)

        # Enhanced overview statistics
        print(f"[STATS] OVERVIEW STATISTICS:")
        print(f"   Total frames extracted:     {summary['total_frames']:>4}")
        print(f"   Frames with annotations:    {summary['annotated_frames']:>4}")
        print(f"   Annotation coverage:        {summary['annotation_density']:>5.1f}%")
        print(f"   Unique objects identified:  {len(summary['unique_objects']):>4}")

        # Detailed annotation breakdown
        total_annotations = summary['total_points'] + summary['total_boxes']
        text_detections = 0
        manual_annotations = 0

        for annotations in frame_annotations.values():
            if annotations.get('source') == 'text_detection':
                text_detections += len(annotations.get('boxes', []))
            else:
                manual_annotations += len(annotations.get('points', [])) + len(annotations.get('boxes', []))

        print(f"\n[TARGET] ANNOTATION BREAKDOWN:")
        print(f"   Total annotations:          {total_annotations:>4}")
        if text_detections > 0:
            print(f"   ├─ Automatic detections:    {text_detections:>4} 🤖")
        if summary['total_points'] > 0:
            print(f"   ├─ Point clicks:            {summary['total_points']:>4}")
            print(f"   │  ├─ Positive clicks:      {summary['positive_clicks']:>4}")
            print(f"   │  └─ Negative clicks:      {summary['negative_clicks']:>4}")
        manual_boxes = summary['total_boxes'] - text_detections
        if manual_boxes > 0:
            print(f"   └─ Manual bounding boxes:   {manual_boxes:>4}")
        elif summary['total_boxes'] > 0 and text_detections == 0:
            print(f"   └─ Bounding boxes:          {summary['total_boxes']:>4}")

        # Object-centric view (similar to screenshot format)
        if summary['unique_objects']:
            sorted_objects = sorted(summary['unique_objects'])
            print(f"\n🏷️  OBJECT-CENTRIC VIEW:")
            print("-" * 60)

            # Show object distribution across frames
            object_frame_count = {}
            object_details = {}

            for obj_id in sorted_objects:
                object_details[obj_id] = {
                    'frames': [],
                    'total_points': 0,
                    'positive_clicks': 0,
                    'negative_clicks': 0,
                    'total_boxes': 0
                }

            for frame_idx, annotations in frame_annotations.items():
                obj_ids = annotations.get('obj_ids', [])
                points = annotations.get('points', [])
                labels = annotations.get('labels', [])
                boxes = annotations.get('boxes', [])

                # Handle points (manual annotations)
                for i, obj_id in enumerate(obj_ids):
                    if obj_id in object_details and i < len(points):
                        if frame_idx not in object_details[obj_id]['frames']:
                            object_details[obj_id]['frames'].append(frame_idx)

                        # Count points for this object
                        object_details[obj_id]['total_points'] += 1
                        if i < len(labels):
                            if labels[i] == 1:
                                object_details[obj_id]['positive_clicks'] += 1
                            elif labels[i] == 0:
                                object_details[obj_id]['negative_clicks'] += 1

                # Handle boxes (can be from text detection or manual)
                for i, obj_id in enumerate(obj_ids):
                    if obj_id in object_details and i < len(boxes):
                        if frame_idx not in object_details[obj_id]['frames']:
                            object_details[obj_id]['frames'].append(frame_idx)

                        # Count boxes for this object
                        object_details[obj_id]['total_boxes'] += 1

            for obj_id in sorted_objects:
                details = object_details[obj_id]
                print(f"\n📌 Object ID: {obj_id}")
                print(f"   Present in frames: {details['frames']}")
                print(f"   Total points: {details['total_points']} "
                      f"({details['positive_clicks']} positive, {details['negative_clicks']} negative)")
                print(f"   Total boxes: {details['total_boxes']}")

        # Enhanced frame-by-frame detailed view
        print(f"\n[INFO] FRAME-BY-FRAME DETAILED VIEW:")
        print("=" * 60)

        for frame_idx in sorted(frame_annotations.keys()):
            annotations = frame_annotations[frame_idx]
            print(f"\n🎬 Frame {frame_idx}:")

            # Points with enhanced detail and object mapping
            points = annotations.get('points', [])
            labels = annotations.get('labels', [])
            obj_ids = annotations.get('obj_ids', [])

            if points and labels:
                frame_positive = sum(1 for label in labels if label == 1)
                frame_negative = sum(1 for label in labels if label == 0)
                print(f"   📍 Points: {len(points)} clicks")

                # Show points with object mapping (similar to screenshot format)
                for i, (point, label) in enumerate(zip(points, labels)):
                    point_type = "positive" if label == 1 else "negative"
                    obj_id = obj_ids[i] if i < len(obj_ids) else "Unknown"
                    print(f"     {i+1}. ({point[0]:4.0f}, {point[1]:4.0f}) {point_type:>8} (Object ID: {obj_id})")
            else:
                print("   📍 Points: 0 clicks")

            # Bounding boxes with enhanced detail and object mapping
            boxes = annotations.get('boxes', [])
            if boxes:
                print(f"   📦 Bounding boxes: {len(boxes)}")
                for i, box in enumerate(boxes):
                    obj_id = obj_ids[i] if i < len(obj_ids) else "Unknown"
                    width = box[2] - box[0]
                    height = box[3] - box[1]
                    print(f"     {i+1}. ({box[0]:4.0f}, {box[1]:4.0f}) to ({box[2]:4.0f}, {box[3]:4.0f}) "
                          f"[{width:4.0f}x{height:4.0f}] (Object ID: {obj_id})")
            else:
                print("   📦 Bounding boxes: 0 boxes")

            # Object summary for frame
            if obj_ids:
                unique_objects_in_frame = list(set(obj_ids))
                print(f"   🏷️  Objects in frame: {unique_objects_in_frame}")
            else:
                print("   🏷️  Objects in frame: None")

        print("\n" + "=" * 80)
        print("[TIP] TIP: Use this summary to verify annotation completeness before propagation")
        print("=" * 80)

    def display_detailed_frame_review(self, frame_annotations, frame_files):
        """Display detailed frame-by-frame review with enhanced formatting."""
        print("\n" + "=" * 80)
        print("DETAILED FRAME-BY-FRAME ANNOTATION REVIEW")
        print("=" * 80)

        if not frame_annotations:
            print("[ERROR] No annotations to review.")
            return

        # Calculate total annotations for summary
        total_annotations = 0
        total_frames = len(frame_annotations)

        for frame_idx in sorted(frame_annotations.keys()):
            annotations = frame_annotations[frame_idx]
            points_count = len(annotations.get('points', []))
            boxes_count = len(annotations.get('boxes', []))
            total_annotations += points_count + boxes_count

        print(f"[STATS] SUMMARY: {total_annotations} annotations across {total_frames} frames")
        print("=" * 80)

        # Display each frame's annotations
        for frame_idx in sorted(frame_annotations.keys()):
            annotations = frame_annotations[frame_idx]
            frame_name = frame_files[frame_idx].name if frame_idx < len(frame_files) else f"Frame_{frame_idx}"

            print(f"\n🎬 Frame {frame_idx:3d}: {frame_name}")
            print("─" * 80)

            annotation_count = 0

            # Points review with enhanced formatting
            if 'points' in annotations and annotations['points']:
                points = annotations['points']
                labels = annotations.get('labels', [])
                obj_ids = annotations.get('obj_ids', [])

                for i, point in enumerate(points):
                    annotation_count += 1
                    label = labels[i] if i < len(labels) else 1
                    obj_id = obj_ids[i] if i < len(obj_ids) else 'Unknown'

                    # Determine click type
                    click_type = "Positive Click" if label == 1 else "Negative Click"
                    click_icon = "[OK]" if label == 1 else "[ERROR]"

                    print(f"  {annotation_count:2d}. Type: Point Click        | Object ID: {obj_id:>3} | {click_icon} {click_type}")
                    print(f"      Coordinates: ({point[0]:6.1f}, {point[1]:6.1f})")

            # Boxes review with enhanced formatting (tensor-safe)
            if 'boxes' in annotations:
                boxes = annotations['boxes']
                try:
                    # Handle tensor/array cases safely
                    if hasattr(boxes, 'cpu'):  # PyTorch tensor
                        boxes = boxes.cpu().numpy().tolist()
                    elif hasattr(boxes, 'tolist'):  # NumPy array
                        boxes = boxes.tolist()

                    # Check if boxes exist and process them
                    if boxes and len(boxes) > 0:
                        obj_ids = annotations.get('obj_ids', [])
                        points_count = len(annotations.get('points', []))
                        detection_labels = annotations.get('detection_labels', [])
                        detection_scores = annotations.get('detection_scores', [])
                        is_text_detection = annotations.get('source') == 'text_detection'

                        for i, box in enumerate(boxes):
                            annotation_count += 1
                            obj_id_idx = points_count + i
                            obj_id = obj_ids[obj_id_idx] if obj_id_idx < len(obj_ids) else 'Unknown'

                            width = box[2] - box[0]
                            height = box[3] - box[1]

                            # Show detection info if available
                            if is_text_detection and i < len(detection_labels):
                                label = detection_labels[i]
                                score = detection_scores[i] if i < len(detection_scores) else 0.0
                                print(f"  {annotation_count:2d}. Type: Auto Detection     | Object ID: {obj_id:>3} | 🤖 Text Detection")
                                print(f"      Label: '{label}' (confidence: {score:.2f})")
                            else:
                                print(f"  {annotation_count:2d}. Type: Bounding Box      | Object ID: {obj_id:>3} | 📦 Box Annotation")

                            print(f"      Top-Left:     ({box[0]:6.1f}, {box[1]:6.1f})")
                            print(f"      Bottom-Right: ({box[2]:6.1f}, {box[3]:6.1f})")
                            print(f"      Dimensions:   {width:6.1f} × {height:6.1f} pixels")

                except Exception as e:
                    print(f"Warning: Error processing boxes for detailed review: {e}")
                    # Fallback: show basic info if available
                    if 'obj_ids' in annotations and annotations['obj_ids']:
                        for obj_id in annotations['obj_ids']:
                            annotation_count += 1
                            print(f"  {annotation_count:2d}. Type: Detection (Error)  | Object ID: {obj_id:>3} | [WARNING]  Processing Error")

            # Show if frame has no annotations (shouldn't happen in this context, but for completeness)
            if annotation_count == 0:
                print("  No annotations found for this frame")

        print("\n" + "=" * 80)
        print(f"[INFO] REVIEW COMPLETE: {total_annotations} total annotations reviewed")
        print("=" * 80)

    def interactive_frame_navigator(self, frame_annotations, frame_files, video_segments=None):
        """Interactive frame-by-frame navigation with mask preview."""
        if not frame_annotations:
            print("[ERROR] No annotations to navigate. Please add annotations first.")
            return

        annotated_frames = sorted(frame_annotations.keys())
        current_idx = 0

        print("\n" + "=" * 80)
        print("INTERACTIVE FRAME NAVIGATOR")
        print("=" * 80)
        print("Commands:")
        print("  'n' or 'next' - Next frame")
        print("  'p' or 'prev' - Previous frame")
        print("  'g <num>' or 'goto <num>' - Go to specific frame")
        print("  'show' - Display current frame with annotations")
        print("  'info' - Show current frame details")
        print("  'list' - List all annotated frames")
        print("  'quit' - Exit navigator")
        print("=" * 80)

        while True:
            if current_idx >= len(annotated_frames):
                current_idx = len(annotated_frames) - 1
            elif current_idx < 0:
                current_idx = 0

            frame_idx = annotated_frames[current_idx]
            frame_name = frame_files[frame_idx].name if frame_idx < len(frame_files) else f"Frame_{frame_idx}"

            print(f"\n📍 Current: Frame {frame_idx} ({current_idx + 1}/{len(annotated_frames)}) - {frame_name}")

            try:
                command = input("Navigator> ").strip().lower()

                if command in ['quit', 'q', 'exit']:
                    break
                elif command in ['n', 'next']:
                    current_idx = min(current_idx + 1, len(annotated_frames) - 1)
                elif command in ['p', 'prev', 'previous']:
                    current_idx = max(current_idx - 1, 0)
                elif command.startswith('g ') or command.startswith('goto '):
                    try:
                        target_frame = int(command.split()[1])
                        if target_frame in annotated_frames:
                            current_idx = annotated_frames.index(target_frame)
                        else:
                            print(f"[ERROR] Frame {target_frame} is not annotated.")
                    except (IndexError, ValueError):
                        print("[ERROR] Invalid goto command. Use 'goto <frame_number>'")
                elif command == 'show':
                    self._display_frame_with_review(frame_idx, frame_annotations, frame_files, video_segments)
                elif command == 'info':
                    self._display_frame_info(frame_idx, frame_annotations, frame_files)
                elif command == 'list':
                    print(f"\n[INFO] Annotated frames: {annotated_frames}")
                else:
                    print("[ERROR] Unknown command. Type 'quit' to exit.")

            except KeyboardInterrupt:
                print("\n👋 Navigator interrupted by user.")
                break
            except Exception as e:
                print(f"[ERROR] Error in navigator: {e}")

    def _display_frame_with_review(self, frame_idx, frame_annotations, frame_files, video_segments=None):
        """Display frame with annotations and optional mask overlay."""
        if frame_idx >= len(frame_files):
            print(f"[ERROR] Frame {frame_idx} not available.")
            return

        frame_file = frame_files[frame_idx]
        annotations = frame_annotations.get(frame_idx, {})

        print(f"\n🖼️  Displaying frame {frame_idx}: {frame_file.name}")

        # Show frame with annotations
        self.predictor.display_frame_with_annotations(
            str(frame_file),
            annotations.get('points', []),
            annotations.get('labels', []),
            annotations.get('boxes', [])
        )

        # If video segments available, show mask overlay
        if video_segments and frame_idx in video_segments:
            print("🎭 Showing mask overlay...")
            self.predictor.visualize_frame_with_masks(
                str(frame_file),
                video_segments[frame_idx],
                show_plot=True
            )

    def _display_frame_info(self, frame_idx, frame_annotations, frame_files):
        """Display detailed information about a specific frame."""
        if frame_idx not in frame_annotations:
            print(f"[ERROR] No annotations for frame {frame_idx}")
            return

        annotations = frame_annotations[frame_idx]
        frame_name = frame_files[frame_idx].name if frame_idx < len(frame_files) else f"Frame_{frame_idx}"

        print(f"\n[INFO] FRAME {frame_idx} DETAILS: {frame_name}")
        print("-" * 50)

        # Points info
        points_count = len(annotations.get('points', []))
        if points_count > 0:
            positive_count = sum(1 for label in annotations.get('labels', []) if label == 1)
            negative_count = points_count - positive_count
            print(f"[TARGET] Click annotations: {points_count} total")
            print(f"   - Positive clicks: {positive_count}")
            print(f"   - Negative clicks: {negative_count}")
        else:
            print("[TARGET] Click annotations: None")

        # Boxes info
        boxes_count = len(annotations.get('boxes', []))
        if boxes_count > 0:
            print(f"📦 Bounding boxes: {boxes_count} total")
            for i, box in enumerate(annotations['boxes']):
                width = box[2] - box[0]
                height = box[3] - box[1]
                area = width * height
                print(f"   - Box {i+1}: {width:.0f}x{height:.0f} (area: {area:.0f})")
        else:
            print("📦 Bounding boxes: None")

        # Object IDs
        unique_objects = set(annotations.get('obj_ids', []))
        if unique_objects:
            print(f"🏷️  Object IDs: {sorted(unique_objects)}")
        else:
            print("🏷️  Object IDs: None")


class VisualAnnotationInterface:
    """Interactive visual annotation interface for SAM2 video frames."""

    def __init__(self):
        self.fig = None
        self.ax = None
        self.frame_image = None
        self.frame_width = 0
        self.frame_height = 0

        # Annotation storage
        self.points = []
        self.labels = []
        self.boxes = []
        self.obj_ids = []

        # Visual elements
        self.point_markers = []
        self.box_patches = []

        # Interaction state
        self.annotation_mode = 'click'  # 'click' or 'box'
        self.box_start = None
        self.current_box_patch = None
        self.is_drawing_box = False
        self.current_object_id = 1  # Default object ID

        # UI elements
        self.buttons = {}
        self.object_id_textbox = None

    def _load_existing_annotations(self, annotations):
        """
        Load existing annotations into the interface for editing.

        Args:
            annotations (dict): Existing annotation data with points, labels, boxes, obj_ids
        """
        try:
            # Load points and labels
            if 'points' in annotations and annotations['points']:
                self.points = list(annotations['points'])
                print(f"   Loaded {len(self.points)} existing points")

            if 'labels' in annotations and annotations['labels']:
                self.labels = list(annotations['labels'])
                print(f"   Loaded {len(self.labels)} existing labels")

            # Load bounding boxes
            if 'boxes' in annotations and annotations['boxes']:
                # Handle tensor/array cases safely
                boxes = annotations['boxes']
                if hasattr(boxes, 'cpu'):  # PyTorch tensor
                    boxes = boxes.cpu().numpy()
                elif hasattr(boxes, 'numpy'):  # Other tensor types
                    boxes = boxes.numpy()

                self.boxes = list(boxes)
                print(f"   Loaded {len(self.boxes)} existing boxes")

            # Load object IDs
            if 'obj_ids' in annotations and annotations['obj_ids']:
                self.obj_ids = list(annotations['obj_ids'])
                print(f"   Loaded {len(self.obj_ids)} existing object IDs")

                # Set current object ID to the highest existing ID + 1
                if self.obj_ids:
                    self.current_object_id = max(self.obj_ids) + 1
                    print(f"   Next object ID will be: {self.current_object_id}")

            print(f"[OK] Successfully loaded existing annotations")

        except Exception as e:
            print(f"[WARNING]  Warning: Error loading existing annotations: {e}")
            # Reset to empty state if loading fails
            self.points = []
            self.labels = []
            self.boxes = []
            self.obj_ids = []

    def _display_existing_annotations(self):
        """
        Display existing annotations visually on the interface.
        """
        try:
            print(f"🎨 Displaying {len(self.points)} existing points and {len(self.boxes)} existing boxes")

            # Display existing points
            for i, (point, label) in enumerate(zip(self.points, self.labels)):
                obj_id = self.obj_ids[i] if i < len(self.obj_ids) else 1

                # Choose color and marker based on label
                if label == 1:  # Positive point
                    color = 'green'
                    marker = 'o'
                    marker_size = 100
                else:  # Negative point
                    color = 'red'
                    marker = 'x'
                    marker_size = 150

                # Add point marker
                point_marker = self.ax.scatter(
                    point[0], point[1],
                    c=color, marker=marker, s=marker_size,
                    edgecolors='white', linewidths=2,
                    label=f'Object {obj_id}', zorder=10
                )
                self.point_markers.append(point_marker)

            # Display existing bounding boxes
            for i, box in enumerate(self.boxes):
                obj_id = self.obj_ids[i] if i < len(self.obj_ids) else 1

                # Create rectangle patch
                from matplotlib.patches import Rectangle
                rect = Rectangle(
                    (box[0], box[1]), box[2] - box[0], box[3] - box[1],
                    linewidth=2, edgecolor='blue', facecolor='none',
                    linestyle='-', alpha=0.8
                )
                self.ax.add_patch(rect)
                self.box_patches.append(rect)

                # Add object ID label
                self.ax.text(
                    box[0], box[1] - 5, f'Object {obj_id}',
                    color='blue', fontsize=10, fontweight='bold',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8)
                )

            # Refresh display
            self.ax.figure.canvas.draw()
            print(f"[OK] Existing annotations displayed successfully")

        except Exception as e:
            print(f"[WARNING]  Warning: Error displaying existing annotations: {e}")

    def setup_interface(self, frame_path, existing_annotations=None):
        """
        Setup the visual annotation interface for a frame.

        Args:
            frame_path (str): Path to the frame image
            existing_annotations (dict): Existing annotations to load and display
        """
        # Load frame
        self.frame_image = Image.open(frame_path)
        frame_array = np.array(self.frame_image)
        self.frame_width, self.frame_height = self.frame_image.size

        # Load existing annotations if provided
        if existing_annotations:
            print(f"[INFO] Loading existing annotations for frame...")
            self._load_existing_annotations(existing_annotations)

        # Create figure and axis with proper backend
        plt.close('all')  # Close any existing figures
        plt.ion()  # Turn on interactive mode

        self.fig, self.ax = plt.subplots(1, 1, figsize=(14, 10))
        self.fig.suptitle(f"Visual Annotation Interface - {Path(frame_path).name}", fontsize=14)

        # Display frame with proper extent to match image coordinates
        self.ax.imshow(frame_array, extent=[0, self.frame_width, self.frame_height, 0])
        self.ax.set_title(f"Frame Size: {self.frame_width}x{self.frame_height} | Mode: {self.annotation_mode}")
        self.ax.set_xlabel("X coordinate")
        self.ax.set_ylabel("Y coordinate")

        # Set axis limits to match image coordinates
        self.ax.set_xlim(0, self.frame_width)
        self.ax.set_ylim(self.frame_height, 0)  # Invert Y axis to match image coordinates

        # Setup event handlers with proper connection
        self.cid_press = self.fig.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.cid_release = self.fig.canvas.mpl_connect('button_release_event', self.on_mouse_release)
        self.cid_motion = self.fig.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)

        # Setup UI buttons
        self.setup_buttons()

        # Clear previous visual annotations
        self.clear_visual_annotations()

        # Display existing annotations if loaded
        if existing_annotations:
            self._display_existing_annotations()

        # Set initial mode
        self.set_annotation_mode('click')

        plt.tight_layout()
        plt.show(block=False)
        plt.draw()  # Force initial draw

    def setup_buttons(self):
        """Setup UI buttons for annotation control."""
        # Button positions (left, bottom, width, height)
        button_height = 0.04
        button_width = 0.12
        button_spacing = 0.02
        start_x = 0.02
        start_y = 0.02

        # Click mode button
        ax_click = plt.axes([start_x, start_y, button_width, button_height])
        self.buttons['click'] = Button(ax_click, 'Click Mode')
        self.buttons['click'].on_clicked(lambda x: self.set_annotation_mode('click'))

        # Box mode button
        ax_box = plt.axes([start_x + button_width + button_spacing, start_y, button_width, button_height])
        self.buttons['box'] = Button(ax_box, 'Box Mode')
        self.buttons['box'].on_clicked(lambda x: self.set_annotation_mode('box'))

        # Clear button
        ax_clear = plt.axes([start_x + 2*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['clear'] = Button(ax_clear, 'Clear All')
        self.buttons['clear'].on_clicked(lambda x: self.clear_annotations())

        # Undo button (kept for backward compatibility with points)
        ax_undo = plt.axes([start_x + 3*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['undo'] = Button(ax_undo, 'Undo Last')
        self.buttons['undo'].on_clicked(lambda x: self.undo_last_annotation())

        # Apply button
        ax_apply = plt.axes([start_x + 4*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['apply'] = Button(ax_apply, 'Apply & Close')
        self.buttons['apply'].on_clicked(lambda x: self.apply_annotations())

        # Cancel button
        ax_cancel = plt.axes([start_x + 5*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['cancel'] = Button(ax_cancel, 'Cancel')
        self.buttons['cancel'].on_clicked(lambda x: self.cancel_annotations())

        # Object ID text box
        textbox_y = start_y + button_height + button_spacing
        ax_textbox = plt.axes([start_x, textbox_y, button_width * 1.5, button_height])
        self.object_id_textbox = TextBox(ax_textbox, 'Obj ID: ', initial=str(self.current_object_id))
        self.object_id_textbox.on_submit(self.update_object_id)

        # Update button colors based on current mode
        self.update_button_colors()

    def update_button_colors(self):
        """Update button colors based on current annotation mode."""
        if 'click' in self.buttons:
            self.buttons['click'].color = 'lightgreen' if self.annotation_mode == 'click' else 'lightgray'
        if 'box' in self.buttons:
            self.buttons['box'].color = 'lightgreen' if self.annotation_mode == 'box' else 'lightgray'

        if self.fig:
            self.fig.canvas.draw()

    def set_annotation_mode(self, mode):
        """Set the annotation mode (click or box)."""
        self.annotation_mode = mode
        self.update_button_colors()

        if mode == 'click':
            self.ax.set_title(f"Click Mode - Left: Positive, Right: Negative | Click box to remove | Frame: {self.frame_width}x{self.frame_height}")
        else:
            self.ax.set_title(f"Box Mode - Click and drag to draw bounding box | Frame: {self.frame_width}x{self.frame_height}")

        self.fig.canvas.draw()

    def update_object_id(self, text):
        """Update the current object ID from text box input."""
        try:
            self.current_object_id = int(text)
            print(f"Object ID updated to: {self.current_object_id}")
        except ValueError:
            print(f"Invalid object ID: {text}. Using default: {self.current_object_id}")
            if self.object_id_textbox:
                self.object_id_textbox.set_val(str(self.current_object_id))

    def get_current_object_id(self):
        """Get the current object ID from the text box, with fallback to stored value."""
        if self.object_id_textbox:
            try:
                # Get the current text value directly from the text box
                current_text = self.object_id_textbox.text
                obj_id = int(current_text)
                # Update the stored value to keep it in sync
                self.current_object_id = obj_id
                return obj_id
            except (ValueError, AttributeError):
                # Fallback to stored value if text box value is invalid
                return self.current_object_id
        else:
            return self.current_object_id

    def on_mouse_press(self, event):
        """Handle mouse press events."""
        # Debug print
        print(f"Mouse press detected: button={event.button}, inaxes={event.inaxes == self.ax}")

        if event.inaxes != self.ax:
            print("Click outside axes, ignoring")
            return

        x, y = event.xdata, event.ydata
        print(f"Raw coordinates: x={x}, y={y}")

        if x is None or y is None:
            print("Invalid coordinates (None), ignoring")
            return

        # Round coordinates to integers for pixel precision
        x, y = round(x), round(y)
        print(f"Rounded coordinates: x={x}, y={y}")

        # Validate coordinates are within frame bounds
        if x < 0 or x >= self.frame_width or y < 0 or y >= self.frame_height:
            print(f"Coordinates out of bounds: ({x}, {y}) not in (0, 0) to ({self.frame_width}, {self.frame_height})")
            return

        # Check if click is on an existing bounding box for deselection
        if self.annotation_mode == 'click' and event.button == 1:  # Left click in click mode
            box_index = self.find_clicked_box(x, y)
            if box_index is not None:
                self.remove_bounding_box(box_index)
                return

        print(f"Processing {self.annotation_mode} annotation at ({x}, {y})")

        if self.annotation_mode == 'click':
            self.handle_click_annotation(x, y, event.button)
        elif self.annotation_mode == 'box':
            self.handle_box_start(x, y, event.button)

    def on_mouse_release(self, event):
        """Handle mouse release events."""
        print(f"Mouse release detected: button={event.button}, mode={self.annotation_mode}")

        if event.inaxes != self.ax or self.annotation_mode != 'box':
            return

        x, y = event.xdata, event.ydata
        if x is None or y is None:
            return

        # Round coordinates to integers
        x, y = round(x), round(y)
        print(f"Box end coordinates: ({x}, {y})")

        self.handle_box_end(x, y, event.button)

    def on_mouse_move(self, event):
        """Handle mouse move events for box drawing."""
        if (event.inaxes != self.ax or self.annotation_mode != 'box' or
            not self.is_drawing_box or self.box_start is None):
            return

        x, y = event.xdata, event.ydata
        if x is None or y is None:
            return

        # Round coordinates to integers
        x, y = round(x), round(y)
        self.update_box_preview(x, y)

    def handle_click_annotation(self, x, y, button):
        """Handle click annotation (positive/negative points)."""
        print(f"Handling click annotation: button={button} at ({x}, {y})")

        # Left click = positive (1), Right click = negative (0)
        label = 1 if button == 1 else 0
        label_text = "positive" if label == 1 else "negative"

        # Get current object ID directly from text box (fixes lag issue)
        obj_id = self.get_current_object_id()
        print(f"Using object ID: {obj_id}")

        # Store annotation
        self.points.append([x, y])
        self.labels.append(label)
        self.obj_ids.append(obj_id)

        # Add visual marker with proper styling
        color = 'green' if label == 1 else 'red'
        marker = '*' if label == 1 else 'X'

        print(f"Creating visual marker: color={color}, marker={marker}")

        # Create the scatter plot marker
        point_marker = self.ax.scatter(x, y, c=color, marker=marker, s=300,
                                     edgecolors='white', linewidth=3, zorder=10,
                                     alpha=0.8)
        self.point_markers.append(point_marker)

        # Add text label for object ID
        text_label = self.ax.text(x + 10, y - 10, f"obj{obj_id}",
                                fontsize=10, color=color, weight='bold',
                                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.7),
                                zorder=11)
        self.point_markers.append(text_label)  # Store text with markers for cleanup

        # Update display
        print(f"Added {label_text} click at ({x}, {y}) for object {obj_id}")

        # Force redraw
        self.fig.canvas.draw_idle()
        self.fig.canvas.flush_events()

        print(f"Total annotations: {len(self.points)} points, {len(self.boxes)} boxes")

    def find_clicked_box(self, x, y):
        """
        Find if a click is inside any existing bounding box.

        Args:
            x, y: Click coordinates

        Returns:
            int or None: Index of the clicked box, or None if no box was clicked
        """
        for i, box in enumerate(self.boxes):
            x1, y1, x2, y2 = box
            # Check if click is inside the bounding box
            if x1 <= x <= x2 and y1 <= y <= y2:
                print(f"Click detected inside bounding box {i}: ({x1}, {y1}, {x2}, {y2})")
                return i
        return None

    def remove_bounding_box(self, box_index):
        """
        Remove a bounding box and its associated object ID from the system.

        Args:
            box_index: Index of the box to remove
        """
        if 0 <= box_index < len(self.boxes):
            # Get the object ID before removal for logging
            obj_id = self.obj_ids[box_index] if box_index < len(self.obj_ids) else "unknown"

            # Remove the box data
            removed_box = self.boxes.pop(box_index)
            removed_obj_id = self.obj_ids.pop(box_index) if box_index < len(self.obj_ids) else None

            # Remove the visual patch
            if box_index < len(self.box_patches):
                patch = self.box_patches.pop(box_index)
                patch.remove()

            print(f"[OK] Removed bounding box {box_index} (Object ID: {obj_id}) at coordinates: {removed_box}")

            # Update display
            if self.fig:
                self.fig.canvas.draw()

            print(f"[STATS] Remaining annotations: {len(self.points)} points, {len(self.boxes)} boxes")
        else:
            print(f"[ERROR] Invalid box index: {box_index}")

    def handle_box_start(self, x, y, button):
        """Handle start of bounding box drawing."""
        print(f"Starting box at ({x}, {y}) with button {button}")

        if button != 1:  # Only left click for boxes
            print("Ignoring non-left click for box mode")
            return

        self.box_start = (x, y)
        self.is_drawing_box = True

        print(f"Box start set to: {self.box_start}")

        # Create preview box patch
        self.current_box_patch = Rectangle((x, y), 0, 0, linewidth=3,
                                         edgecolor='blue', facecolor='none',
                                         linestyle='--', zorder=5, alpha=0.7)
        self.ax.add_patch(self.current_box_patch)

        print("Preview box patch created")

        # Force redraw
        self.fig.canvas.draw_idle()
        self.fig.canvas.flush_events()

    def handle_box_end(self, x, y, button):
        """Handle end of bounding box drawing."""
        print(f"Ending box at ({x}, {y}) with button {button}")

        if not self.is_drawing_box or self.box_start is None or button != 1:
            print("Invalid box end conditions")
            return

        x1, y1 = self.box_start
        x2, y2 = x, y

        print(f"Box coordinates: ({x1}, {y1}) to ({x2}, {y2})")

        # Ensure proper box format (x1 < x2, y1 < y2)
        if x1 > x2:
            x1, x2 = x2, x1
        if y1 > y2:
            y1, y2 = y2, y1

        print(f"Normalized box coordinates: ({x1}, {y1}) to ({x2}, {y2})")

        # Validate box size
        width, height = abs(x2 - x1), abs(y2 - y1)
        if width < 5 or height < 5:
            print(f"Box too small ({width}x{height}). Please draw a larger bounding box.")
            self.cancel_current_box()
            return

        print(f"Box size valid: {width}x{height}")

        # Get current object ID directly from text box (fixes lag issue)
        obj_id = self.get_current_object_id()
        print(f"Using object ID: {obj_id}")

        # Store annotation
        box = [x1, y1, x2, y2]
        self.boxes.append(box)
        self.obj_ids.append(obj_id)

        # Replace preview with final box
        if self.current_box_patch:
            self.current_box_patch.remove()
            print("Removed preview box")

        final_box_patch = Rectangle((x1, y1), x2-x1, y2-y1, linewidth=3,
                                  edgecolor='green', facecolor='none', zorder=5,
                                  alpha=0.8)
        self.ax.add_patch(final_box_patch)
        self.box_patches.append(final_box_patch)

        # Add text label for object ID
        text_x, text_y = x1 + 5, y1 - 5
        text_label = self.ax.text(text_x, text_y, f"box{obj_id}",
                                fontsize=10, color='green', weight='bold',
                                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.7),
                                zorder=11)
        self.box_patches.append(text_label)  # Store text with patches for cleanup

        # Reset state
        self.box_start = None
        self.is_drawing_box = False
        self.current_box_patch = None

        print(f"Added bounding box ({x1}, {y1}) to ({x2}, {y2}) for object {obj_id}")

        # Force redraw
        self.fig.canvas.draw_idle()
        self.fig.canvas.flush_events()

        print(f"Total annotations: {len(self.points)} points, {len(self.boxes)} boxes")

    def update_box_preview(self, x, y):
        """Update the preview of the bounding box being drawn."""
        if self.current_box_patch and self.box_start:
            x1, y1 = self.box_start

            # Calculate proper rectangle coordinates
            min_x = min(x1, x)
            min_y = min(y1, y)
            width = abs(x - x1)
            height = abs(y - y1)

            # Update the preview rectangle
            self.current_box_patch.set_xy((min_x, min_y))
            self.current_box_patch.set_width(width)
            self.current_box_patch.set_height(height)

            # Force redraw for smooth preview
            self.fig.canvas.draw_idle()

    def cancel_current_box(self):
        """Cancel the current box being drawn."""
        if self.current_box_patch:
            self.current_box_patch.remove()
            self.current_box_patch = None
        self.box_start = None
        self.is_drawing_box = False
        self.fig.canvas.draw()

    def clear_annotations(self):
        """Clear all annotations for the current frame."""
        # Clear data
        self.points.clear()
        self.labels.clear()
        self.boxes.clear()
        self.obj_ids.clear()

        # Clear visual elements
        self.clear_visual_annotations()
        print("All annotations cleared.")

    def clear_visual_annotations(self):
        """Clear visual annotation elements from the plot."""
        print("Clearing visual annotations...")

        # Remove point markers
        for marker in self.point_markers:
            try:
                marker.remove()
            except Exception as e:
                print(f"Error removing point marker: {e}")
        self.point_markers.clear()

        # Remove box patches
        for patch in self.box_patches:
            try:
                patch.remove()
            except Exception as e:
                print(f"Error removing box patch: {e}")
        self.box_patches.clear()

        # Cancel any current box
        self.cancel_current_box()

        if self.fig:
            self.fig.canvas.draw_idle()
            self.fig.canvas.flush_events()

        print("Visual annotations cleared")

    def undo_last_annotation(self):
        """Undo the last annotation (points only - use click-to-remove for boxes)."""
        if self.points:
            # Remove last point
            self.points.pop()
            self.labels.pop()
            self.obj_ids.pop()

            # Remove visual marker
            if self.point_markers:
                marker = self.point_markers.pop()
                marker.remove()

            print("[OK] Undid last click annotation.")

        elif self.boxes:
            print("[TIP] To remove bounding boxes, click directly on them in Click Mode instead of using Undo.")
            print("   This ensures proper object ID cleanup and prevents mask propagation issues.")

        else:
            print("No point annotations to undo.")

        if self.fig:
            self.fig.canvas.draw()

    def apply_annotations(self):
        """Apply annotations and close the interface."""
        print(f"Applying annotations: {len(self.points)} points, {len(self.boxes)} boxes")
        self.annotation_complete = True
        plt.close(self.fig)

    def cancel_annotations(self):
        """Cancel annotations and close the interface."""
        print("Annotations cancelled by user")
        self.clear_annotations()
        self.annotation_complete = False
        plt.close(self.fig)

    def get_annotations(self):
        """Get the current annotations."""
        return {
            'points': self.points.copy(),
            'labels': self.labels.copy(),
            'boxes': self.boxes.copy(),
            'obj_ids': self.obj_ids.copy()
        }

    def wait_for_completion(self):
        """Wait for user to complete annotations."""
        self.annotation_complete = None

        # Show instructions
        print("\n" + "=" * 60)
        print("Visual Annotation Interface")
        print("=" * 60)
        print("Instructions:")
        print("• Click Mode: Left click = positive point, Right click = negative point")
        print("• Click Mode: Left click on existing bounding box = remove that box")
        print("• Box Mode: Click and drag to draw bounding boxes")
        print("• Use buttons to switch modes, clear, undo, apply, or cancel")
        print("• Close the window or click 'Apply & Close' when finished")
        print("=" * 60)
        print("Interface is ready for interaction...")

        # Ensure the figure is properly displayed
        if self.fig:
            self.fig.show()
            self.fig.canvas.draw()
            self.fig.canvas.flush_events()

        # Keep the interface open until user completes or cancels
        while self.annotation_complete is None:
            try:
                plt.pause(0.1)

                # Check if figure still exists
                if not plt.get_fignums():  # Figure was closed
                    print("Figure was closed, saving annotations")
                    # If user has annotations and didn't explicitly cancel, save them
                    if self.points or self.boxes:
                        print(f"Saving {len(self.points)} points and {len(self.boxes)} boxes")
                        self.annotation_complete = True
                    else:
                        print("No annotations to save")
                        self.annotation_complete = False
                    break

            except KeyboardInterrupt:
                print("\nInterrupted by user")
                self.annotation_complete = False
                break
            except Exception as e:
                print(f"Error in annotation interface: {e}")
                self.annotation_complete = False
                break

        return self.annotation_complete

    def validate_coordinates(self, coords, frame_width, frame_height):
        """
        Validate that coordinates are within frame boundaries.

        Args:
            coords (list): List of coordinates to validate
            frame_width (int): Frame width
            frame_height (int): Frame height

        Returns:
            bool: True if all coordinates are valid
        """
        for coord in coords:
            x, y = coord[:2]  # Take first two elements (x, y)
            if x < 0 or x >= frame_width or y < 0 or y >= frame_height:
                print(f"Error: Coordinate ({x}, {y}) is outside frame boundaries")
                print(f"Frame size: {frame_width} x {frame_height}")
                return False
        return True


def run_interactive_demo(video_path, model_size="base_plus", max_frames=100, output_dir=None, input_size=None):
    """
    Run interactive demo with visual annotation interface.

    Args:
        video_path (str): Path to the input video file
        model_size (str): SAM2 model size
        max_frames (int): Maximum number of frames to extract
        output_dir (str): Output directory for results
        input_size (int): Input size for the model
    """
    print("=" * 80)
    print("SAM2 Video Predictor v3 - Interactive Demo")
    print("=" * 80)

    try:
        # Initialize directory manager
        video_name = Path(video_path).stem
        if not output_dir:
            output_dir = f"./sam2_output"

        dir_manager = DirectoryManager(output_dir)
        dir_manager.setup_directories(video_name)

        # Initialize SAM2 predictor
        predictor = SAM2VideoPredictor(
            model_size=model_size,
            input_size=input_size
        )

        # Preprocess video to frames
        print(f"\nPreprocessing video...")
        frames_dir = predictor.preprocess_video(
            video_path,
            max_frames=max_frames,
            output_dir=str(dir_manager.base_output_dir)
        )
        dir_manager.set_video_dir(frames_dir)

        # Initialize video state
        print(f"\nInitializing video state...")
        predictor.init_video_state(frames_dir)

        # Get frame files
        frames_path = Path(frames_dir)
        frame_files = sorted([f for f in frames_path.glob("*") if f.suffix.lower() in ['.jpg', '.jpeg', '.png']])

        if not frame_files:
            print("[ERROR] No frame files found!")
            return

        print(f"Found {len(frame_files)} frames for annotation")

        # Display frame list
        predictor.display_frame_list(frame_files)

        # Interactive annotation loop
        frame_annotations = {}
        current_frame_idx = 0

        # Convert text-based detection results to annotation format for review
        if hasattr(predictor, '_last_detection_results') and predictor._last_detection_results:
            print("[INFO] Converting text-based detection results for review...")
            frame_annotations = _convert_detection_results_to_annotations(predictor._last_detection_results)
            print(f"[OK] Loaded {len(frame_annotations)} frames with automatic detections for review")

        while True:
            print(f"\n" + "=" * 60)
            print(f"Interactive Annotation - Frame {current_frame_idx}/{len(frame_files)-1}")
            print("=" * 60)
            print("Options:")
            print("1. Annotate current frame")
            print("2. Go to specific frame")
            print("3. Review annotations and results")
            print("4. Reset state (allow new object IDs)")
            print("5. Propagate masks and export")
            print("6. Exit")

            choice = input("Select option (1-6): ").strip()

            if choice == '1':
                # Annotate current frame
                frame_file = frame_files[current_frame_idx]
                print(f"\nAnnotating frame: {frame_file.name}")

                # Use visual annotation interface
                interface = VisualAnnotationInterface()

                # Check for existing annotations for this frame
                existing_annotations = frame_annotations.get(current_frame_idx, None)
                interface.setup_interface(str(frame_file), existing_annotations)

                # Wait for user to complete annotations
                completed = interface.wait_for_completion()

                if completed:
                    annotations = interface.get_annotations()
                    if annotations['points'] or annotations['boxes']:
                        frame_annotations[current_frame_idx] = annotations
                        print(f"[OK] Saved annotations for frame {current_frame_idx}")

                        # Apply annotations to predictor
                        _apply_annotations_to_predictor(
                            predictor, current_frame_idx, annotations
                        )
                    else:
                        print("No annotations to save")
                else:
                    print("Annotation cancelled")

                # Move to next frame
                if current_frame_idx < len(frame_files) - 1:
                    current_frame_idx += 1

            elif choice == '2':
                # Go to specific frame
                try:
                    frame_idx = int(input(f"Enter frame index (0-{len(frame_files)-1}): "))
                    if 0 <= frame_idx < len(frame_files):
                        current_frame_idx = frame_idx
                        print(f"Moved to frame {current_frame_idx}")
                    else:
                        print("Invalid frame index")
                except ValueError:
                    print("Invalid input")

            elif choice == '3':
                # Review annotations and results
                print(f"\n" + "=" * 60)
                print("ANNOTATION REVIEW & ANALYSIS")
                print("=" * 60)

                # Initialize review system
                review_system = AnnotationReviewSystem(predictor, dir_manager)

                if not frame_annotations:
                    print("[ERROR] No annotations to review. Please add annotations first.")
                else:
                    # Show review menu
                    while True:
                        print(f"\nReview Options:")
                        print("1. Show annotation summary")
                        print("2. Detailed frame-by-frame review")
                        print("3. Interactive frame navigator")
                        print("4. Back to main menu")

                        review_choice = input("Select review option (1-4): ").strip()

                        if review_choice == '1':
                            review_system.display_annotation_summary(frame_annotations, frame_files)
                        elif review_choice == '2':
                            review_system.display_detailed_frame_review(frame_annotations, frame_files)
                        elif review_choice == '3':
                            # Check if we have propagated masks for enhanced review
                            video_segments = getattr(predictor, '_last_video_segments', None)
                            review_system.interactive_frame_navigator(frame_annotations, frame_files, video_segments)
                        elif review_choice == '4':
                            break
                        else:
                            print("[ERROR] Invalid choice. Please select 1-4.")

            elif choice == '4':
                # Reset state with annotation preservation
                print(f"\nResetting video state while preserving existing detections...")
                try:
                    predictor.reset_state(preserve_annotations=True)
                    print("[OK] Video state reset successfully!")
                    print("Previous automatic detections preserved. You can now add manual refinements.")

                    # Refresh frame annotations with restored detection results
                    if hasattr(predictor, '_last_detection_results') and predictor._last_detection_results:
                        print("[INFO] Refreshing frame annotations with restored detections...")
                        frame_annotations = _convert_detection_results_to_annotations(predictor._last_detection_results)
                        print(f"[OK] Refreshed {len(frame_annotations)} frames with restored detections")

                except Exception as e:
                    print(f"[ERROR] Error resetting state: {e}")

            elif choice == '5':
                # Propagate masks and export
                if frame_annotations:
                    print(f"\nPropagating masks for {len(frame_annotations)} annotated frames...")

                    try:
                        # Apply refined annotations with proper state clearing
                        print("[INFO] Applying refined annotations to SAM2 predictor...")
                        refined_objects = _apply_refined_annotations_to_predictor(predictor, frame_annotations)
                        print(f"[OK] Refined annotations applied successfully for {len(refined_objects)} objects")

                        # Propagate masks
                        video_segments = predictor.propagate_masks()

                        # Store video segments for review functionality
                        predictor._last_video_segments = video_segments

                        # Save masks and JSON data (NEW FUNCTIONALITY)
                        paths = dir_manager.get_paths()
                        predictor.save_masks_and_json(
                            video_segments,
                            frames_dir,
                            paths['mask_data_dir'],
                            paths['json_data_dir']
                        )

                        # Save PNG mask visualization files
                        predictor.save_png_masks(video_segments, frames_dir, paths['mask_data_dir'])

                        # Export PNG masks for visualization
                        predictor.export_masks(video_segments, paths['mask_data_dir'])

                        # Export visualizations
                        overlay_dir = predictor.export_masks_with_overlay(
                            video_segments, frames_dir, paths['result_dir']
                        )

                        # Create video if requested
                        output_video_path = str(dir_manager.base_output_dir / f"{video_name}_annotated.mp4")
                        create_video_from_images(overlay_dir, output_video_path, 25)

                        print(f"[OK] Processing completed!")
                        print(f"Results saved to: {dir_manager.base_output_dir}")
                        print(f"Video created: {output_video_path}")
                        print(f"Mask data (.npy): {paths['mask_data_dir']}")
                        print(f"JSON metadata: {paths['json_data_dir']}")
                        print(f"Visualizations: {paths['result_dir']}")

                    except Exception as e:
                        print(f"[ERROR] Error during propagation: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    print("No annotations to propagate")

            elif choice == '6':
                print("Exiting interactive demo")
                break
            else:
                print("Invalid choice")

        print(f"\nInteractive demo completed!")

    except Exception as e:
        print(f"[ERROR] Error in interactive demo: {e}")
        import traceback
        traceback.print_exc()


def run_interactive_demo_with_detections(video_path, model_size="base_plus", max_frames=100,
                                       output_dir=None, input_size=None, predictor=None,
                                       dir_manager=None, detection_results=None, frames_dir_input=None):
    """
    Run interactive demo with pre-existing detection results for refinement.

    Args:
        video_path (str): Path to the input video file or frames directory
        model_size (str): SAM2 model size
        max_frames (int): Maximum number of frames to extract
        output_dir (str): Output directory for results
        frames_dir_input (str): Direct frames directory path (for frame input mode)
        input_size (int): Input size for the model
        predictor (SAM2VideoPredictor): Pre-initialized predictor with detection results
        dir_manager (DirectoryManager): Pre-initialized directory manager
        detection_results (dict): Pre-existing detection results from text-based detection
    """
    print("=" * 80)
    print("SAM2 Video Predictor v5 - Interactive Refinement Mode")
    print("=" * 80)

    try:
        # Use existing predictor and directory manager if provided
        if predictor is None or dir_manager is None:
            print("[ERROR] Error: Predictor and directory manager must be provided for refinement mode")
            return

        # Get frame files from the existing video directory or frames directory
        frames_dir = dir_manager.video_dir if dir_manager.video_dir else frames_dir_input
        frames_path = Path(frames_dir)
        frame_files = sorted([f for f in frames_path.glob("*") if f.suffix.lower() in ['.jpg', '.jpeg', '.png']])

        if not frame_files:
            print("[ERROR] No frame files found!")
            return

        print(f"Found {len(frame_files)} frames for annotation")

        # Display frame list
        predictor.display_frame_list(frame_files)

        # Interactive annotation loop
        frame_annotations = {}
        current_frame_idx = 0

        # Convert text-based detection results to annotation format for review
        if detection_results:
            print("[INFO] Converting text-based detection results for review...")
            frame_annotations = _convert_detection_results_to_annotations(detection_results)
            print(f"[OK] Loaded {len(frame_annotations)} frames with automatic detections for review")
        else:
            print("[WARNING]  No detection results provided - starting with empty annotations")

        # Reset state to allow refinements while preserving detections
        print("[RESET] Resetting video state while preserving detections for refinement...")
        predictor.reset_state(preserve_annotations=True)

        while True:
            print(f"\n" + "=" * 60)
            print(f"Interactive Refinement - Frame {current_frame_idx}/{len(frame_files)-1}")
            print("=" * 60)
            print("Options:")
            print("1. Annotate current frame")
            print("2. Go to specific frame")
            print("3. Review annotations and results")
            print("4. Reset state (allow new object IDs)")
            print("5. Propagate masks and export")
            print("6. Exit")

            choice = input("Select option (1-6): ").strip()

            if choice == '1':
                # Annotate current frame
                frame_file = frame_files[current_frame_idx]
                print(f"\nAnnotating frame: {frame_file.name}")

                # Use visual annotation interface
                interface = VisualAnnotationInterface()

                # Check for existing annotations for this frame
                existing_annotations = frame_annotations.get(current_frame_idx, None)
                interface.setup_interface(str(frame_file), existing_annotations)

                # Wait for user to complete annotations
                completed = interface.wait_for_completion()

                if completed:
                    annotations = interface.get_annotations()
                    if annotations['points'] or annotations['boxes']:
                        frame_annotations[current_frame_idx] = annotations
                        print(f"[OK] Saved annotations for frame {current_frame_idx}")

                        # Apply annotations to predictor
                        _apply_annotations_to_predictor(
                            predictor, current_frame_idx, annotations
                        )
                    else:
                        print("No annotations to save")
                else:
                    print("Annotation cancelled")

                # Move to next frame
                if current_frame_idx < len(frame_files) - 1:
                    current_frame_idx += 1

            elif choice == '2':
                # Go to specific frame
                try:
                    frame_idx = int(input(f"Enter frame index (0-{len(frame_files)-1}): "))
                    if 0 <= frame_idx < len(frame_files):
                        current_frame_idx = frame_idx
                        print(f"Moved to frame {current_frame_idx}")
                    else:
                        print("Invalid frame index")
                except ValueError:
                    print("Invalid input")

            elif choice == '3':
                # Review annotations and results
                print(f"\n" + "=" * 60)
                print("ANNOTATION REVIEW & ANALYSIS")
                print("=" * 60)

                # Initialize review system
                review_system = AnnotationReviewSystem(predictor, dir_manager)

                if not frame_annotations:
                    print("[ERROR] No annotations to review. Please add annotations first.")
                else:
                    # Show review menu
                    while True:
                        print(f"\nReview Options:")
                        print("1. Show annotation summary")
                        print("2. Detailed frame-by-frame review")
                        print("3. Interactive frame navigator")
                        print("4. Back to main menu")

                        review_choice = input("Select review option (1-4): ").strip()

                        if review_choice == '1':
                            review_system.display_annotation_summary(frame_annotations, frame_files)
                        elif review_choice == '2':
                            review_system.display_detailed_frame_review(frame_annotations, frame_files)
                        elif review_choice == '3':
                            # Check if we have propagated masks for enhanced review
                            video_segments = getattr(predictor, '_last_video_segments', None)
                            review_system.interactive_frame_navigator(frame_annotations, frame_files, video_segments)
                        elif review_choice == '4':
                            break
                        else:
                            print("[ERROR] Invalid choice. Please select 1-4.")

            elif choice == '4':
                # Reset state with annotation preservation
                print(f"\nResetting video state while preserving existing detections...")
                try:
                    predictor.reset_state(preserve_annotations=True)
                    print("[OK] Video state reset successfully!")
                    print("Previous automatic detections preserved. You can now add manual refinements.")

                    # Refresh frame annotations with restored detection results
                    if hasattr(predictor, '_last_detection_results') and predictor._last_detection_results:
                        print("[INFO] Refreshing frame annotations with restored detections...")
                        frame_annotations = _convert_detection_results_to_annotations(predictor._last_detection_results)
                        print(f"[OK] Refreshed {len(frame_annotations)} frames with restored detections")

                except Exception as e:
                    print(f"[ERROR] Error resetting state: {e}")

            elif choice == '5':
                # Propagate masks and export
                if frame_annotations:
                    print(f"\nPropagating masks for {len(frame_annotations)} annotated frames...")

                    try:
                        # Apply refined annotations with proper state clearing
                        print("[INFO] Applying refined annotations to SAM2 predictor...")
                        refined_objects = _apply_refined_annotations_to_predictor(predictor, frame_annotations)
                        print(f"[OK] Refined annotations applied successfully for {len(refined_objects)} objects")

                        # Propagate masks
                        video_segments = predictor.propagate_masks()

                        # Store video segments for review functionality
                        predictor._last_video_segments = video_segments

                        # Save masks and JSON data
                        paths = dir_manager.get_paths()
                        predictor.save_masks_and_json(
                            video_segments,
                            frames_dir,
                            paths['mask_data_dir'],
                            paths['json_data_dir']
                        )

                        # Save PNG mask visualization files
                        predictor.save_png_masks(video_segments, frames_dir, paths['mask_data_dir'])

                        # Export PNG masks for visualization
                        predictor.export_masks(video_segments, paths['mask_data_dir'])

                        # Export visualizations
                        overlay_dir = predictor.export_masks_with_overlay(
                            video_segments, frames_dir, paths['result_dir']
                        )

                        # Create video
                        video_name = Path(video_path).stem
                        output_video_path = str(dir_manager.base_output_dir / f"{video_name}_refined.mp4")
                        create_video_from_images(overlay_dir, output_video_path, 25)

                        print(f"[OK] Processing completed!")
                        print(f"Results saved to: {dir_manager.base_output_dir}")
                        print(f"Video created: {output_video_path}")
                        print(f"Mask data (.npy): {paths['mask_data_dir']}")
                        print(f"JSON metadata: {paths['json_data_dir']}")

                    except Exception as e:
                        print(f"[ERROR] Error during propagation: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    print("[ERROR] No annotations to propagate. Please add annotations first.")

            elif choice == '6':
                print("👋 Exiting interactive refinement mode...")
                break

            else:
                print("[ERROR] Invalid choice. Please select 1-6.")

    except Exception as e:
        print(f"[ERROR] Error in interactive refinement demo: {e}")
        import traceback
        traceback.print_exc()


def _convert_detection_results_to_annotations(detection_results):
    """Convert text-based detection results to annotation format for review system."""
    frame_annotations = {}

    for frame_idx, detection_data in detection_results.items():
        if not detection_data.get('object_ids'):
            continue

        # Convert tensors to lists for compatibility with review system
        boxes = detection_data.get('boxes', [])
        if hasattr(boxes, 'cpu'):  # Check if it's a tensor
            boxes = boxes.cpu().numpy().tolist()
        elif hasattr(boxes, 'tolist'):  # Check if it's a numpy array
            boxes = boxes.tolist()

        scores = detection_data.get('scores', [])
        if hasattr(scores, 'cpu'):  # Check if it's a tensor
            scores = scores.cpu().numpy().tolist()
        elif hasattr(scores, 'tolist'):  # Check if it's a numpy array
            scores = scores.tolist()

        # Convert detection data to annotation format
        annotations = {
            'points': [],  # Text-based detection doesn't use points
            'labels': [],  # Text-based detection doesn't use point labels
            'boxes': boxes,
            'obj_ids': detection_data.get('object_ids', []),
            'detection_labels': detection_data.get('labels', []),  # Store original detection labels
            'detection_scores': scores,  # Store detection scores (converted to list)
            'source': 'text_detection'  # Mark as automatic detection
        }

        frame_annotations[frame_idx] = annotations

    return frame_annotations

def _apply_annotations_to_predictor(predictor, frame_idx, annotations, clear_existing=False):
    """Apply annotations to the SAM2 predictor with enhanced point-based refinement support."""
    try:
        # Clear existing annotations for this frame if requested (for refinement mode)
        if clear_existing:
            print(f"    [RESET] Clearing existing annotations for frame {frame_idx} before applying refined annotations")
            # Reset the entire predictor state to ensure clean application
            predictor.predictor.reset_state(predictor.inference_state)

            # Re-initialize the video state
            predictor.init_video_state(predictor.video_frames_dir)
            print(f"    [OK] Predictor state cleared and re-initialized")

        # Apply point annotations with detailed logging for verification
        if annotations.get('points') and annotations.get('labels'):
            points = annotations['points']
            labels = annotations['labels']
            obj_ids = annotations.get('obj_ids', [])

            print(f"    Applying {len(points)} point annotations to frame {frame_idx}")

            # Count positive and negative clicks for verification
            positive_clicks = sum(1 for label in labels if label == 1)
            negative_clicks = sum(1 for label in labels if label == 0)
            print(f"      Positive clicks: {positive_clicks}, Negative clicks: {negative_clicks}")

            # Group by object ID
            obj_points = {}
            obj_labels = {}

            for i, (point, label, obj_id) in enumerate(zip(points, labels, obj_ids)):
                if obj_id not in obj_points:
                    obj_points[obj_id] = []
                    obj_labels[obj_id] = []
                obj_points[obj_id].append(point)
                obj_labels[obj_id].append(label)

                # Log each click for verification
                click_type = "positive" if label == 1 else "negative"
                print(f"        Click {i+1}: {click_type} at ({point[0]:.1f}, {point[1]:.1f}) for object {obj_id}")

            # Add annotations for each object
            for obj_id in obj_points:
                print(f"      Adding {len(obj_points[obj_id])} clicks for object {obj_id}")
                result = predictor.add_click_annotation(
                    frame_idx,
                    obj_points[obj_id],
                    obj_labels[obj_id],
                    obj_id
                )
                print(f"        [OK] Click annotations applied successfully for object {obj_id}")

        # Apply box annotations (from text-based detection or manual boxes)
        if annotations.get('boxes'):
            boxes = annotations['boxes']
            obj_ids = annotations.get('obj_ids', [])

            # Handle different annotation sources
            if annotations.get('source') == 'text_detection':
                # Text-based detection: boxes already have corresponding obj_ids
                print(f"    Applying {len(boxes)} text-detection boxes to frame {frame_idx}")
                for i, (box, obj_id) in enumerate(zip(boxes, obj_ids)):
                    print(f"      Box {i+1}: Object {obj_id} at [{box[0]:.1f}, {box[1]:.1f}, {box[2]:.1f}, {box[3]:.1f}]")
                    predictor.add_box_annotation(frame_idx, box, obj_id)
                    print(f"        [OK] Box annotation applied for object {obj_id}")
            else:
                # Manual box annotations
                print(f"    Applying {len(boxes)} manual boxes to frame {frame_idx}")
                # Get unique object IDs for boxes (skip point obj_ids)
                point_count = len(annotations.get('points', []))
                box_obj_ids = obj_ids[point_count:] if len(obj_ids) > point_count else obj_ids

                for box, obj_id in zip(boxes, box_obj_ids):
                    predictor.add_box_annotation(frame_idx, box, obj_id)
                    print(f"        [OK] Box annotation applied for object {obj_id}")

    except Exception as e:
        print(f"[ERROR] Error applying annotations to frame {frame_idx}: {e}")
        import traceback
        traceback.print_exc()


def _apply_refined_annotations_to_predictor(predictor, frame_annotations):
    """
    Apply refined annotations to the SAM2 predictor with proper state clearing.

    This function ensures that only the refined annotations are present in the predictor
    by clearing the existing state and applying only the current frame_annotations.

    Args:
        predictor (SAM2VideoPredictor): The video predictor instance
        frame_annotations (dict): Dictionary of refined annotations per frame
    """
    print("[RESET] Applying refined annotations with state clearing...")

    # Clear the entire predictor state to remove old detection data
    print("  Clearing existing predictor state...")
    predictor.predictor.reset_state(predictor.inference_state)

    # Re-initialize the video state
    print("  Re-initializing video state...")
    predictor.init_video_state(predictor.video_frames_dir)

    # Apply refined annotations frame by frame
    total_objects = set()
    for frame_idx, annotations in frame_annotations.items():
        print(f"  [INFO] Applying refined annotations to frame {frame_idx}")

        # Track unique object IDs
        if annotations.get('obj_ids'):
            total_objects.update(annotations['obj_ids'])

        # Apply annotations without clearing (since we already cleared globally)
        _apply_annotations_to_predictor(predictor, frame_idx, annotations, clear_existing=False)

    print(f"[OK] Refined annotations applied successfully!")
    print(f"  [STATS] Total unique objects in refined annotations: {len(total_objects)}")
    print(f"  [TARGET] Object IDs: {sorted(total_objects)}")

    return total_objects


def test_point_based_refinement(predictor, frame_files, detection_results):
    """
    Test point-based refinement integration with text-based detection results.

    Args:
        predictor (SAM2VideoPredictor): The video predictor instance
        frame_files (list): List of frame file paths
        detection_results (dict): Text-based detection results

    Returns:
        bool: True if test passes, False otherwise
    """
    print("\n" + "=" * 60)
    print("TESTING POINT-BASED REFINEMENT INTEGRATION")
    print("=" * 60)

    try:
        if not detection_results:
            print("[ERROR] No detection results available for testing")
            return False

        # Find a frame with detections for testing
        test_frame_idx = None
        for frame_idx, detection_data in detection_results.items():
            if detection_data.get('object_ids'):
                test_frame_idx = frame_idx
                break

        if test_frame_idx is None:
            print("[ERROR] No frames with detections found for testing")
            return False

        print(f"Testing with frame {test_frame_idx}")

        # Convert detection results to annotations
        frame_annotations = _convert_detection_results_to_annotations(detection_results)

        if test_frame_idx not in frame_annotations:
            print("[ERROR] Test frame not found in converted annotations")
            return False

        # Get the test frame annotations
        test_annotations = frame_annotations[test_frame_idx]
        print(f"Original annotations: {len(test_annotations.get('obj_ids', []))} objects")

        # Add test positive and negative clicks
        test_points = [[100, 100], [200, 200]]  # Example coordinates
        test_labels = [1, 0]  # Positive and negative clicks
        test_obj_ids = [1, 1]  # Both for object 1

        # Create enhanced annotations with manual points
        enhanced_annotations = {
            'points': test_points,
            'labels': test_labels,
            'boxes': test_annotations.get('boxes', []),
            'obj_ids': test_annotations.get('obj_ids', []) + test_obj_ids,
            'detection_labels': test_annotations.get('detection_labels', []),
            'detection_scores': test_annotations.get('detection_scores', []),
            'source': 'mixed'  # Mixed automatic + manual
        }

        print(f"Enhanced annotations: {len(enhanced_annotations['points'])} points, {len(enhanced_annotations.get('boxes', []))} boxes")
        print(f"  Positive clicks: {sum(1 for label in test_labels if label == 1)}")
        print(f"  Negative clicks: {sum(1 for label in test_labels if label == 0)}")

        # Test applying annotations to predictor
        print("\nTesting annotation application...")
        _apply_annotations_to_predictor(predictor, test_frame_idx, enhanced_annotations)

        print("[OK] Point-based refinement integration test completed successfully")
        print("[OK] Both positive and negative clicks processed correctly")
        print("[OK] Mixed automatic detection + manual refinement workflow verified")

        return True

    except Exception as e:
        print(f"[ERROR] Point-based refinement test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="SAM2 Video Predictor v6 - Enhanced video segmentation with text-based detection and manual refinement",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Text-based automatic detection
  python video_predictor_sam2_v6.py --video input.mp4 --text-prompt "person. car. dog."

  # Text-based detection with specific prompt frame
  python video_predictor_sam2_v6.py --video input.mp4 --text-prompt "skirt." --prompt-frame 10

  # Manual annotation mode (original functionality)
  python video_predictor_sam2_v6.py --video input.mp4 --manual-mode

  # Hybrid mode: automatic detection + manual refinement
  python video_predictor_sam2_v6.py --video input.mp4 --text-prompt "person. car." --allow-refinement

  # Use specific model size with text detection
  python video_predictor_sam2_v5.py --video input.mp4 --model base_plus --text-prompt "vehicle. person."

  # Specify detection parameters
  python video_predictor_sam2_v5.py --video input.mp4 --text-prompt "dog. cat." --box-threshold 0.3 --frame-step 10
        """
    )

    # Input source arguments (one required)
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        "--video",
        type=str,
        help="Path to input video file"
    )
    input_group.add_argument(
        "--frames-dir",
        type=str,
        help="Path to directory containing video frames (alternative to --video)"
    )

    # Model configuration
    parser.add_argument(
        "--model",
        type=str,
        choices=["tiny", "small", "base_plus", "large", "custom"],
        default="base_plus",
        help="SAM2 model size to use (default: base_plus)"
    )

    parser.add_argument(
        "--input_size",
        type=int,
        help="Input size for the Video Segmentation model (optional)"
    )

    # Processing options
    parser.add_argument(
        "--max_frames",
        type=int,
        default=100,
        help="Maximum number of frames to extract from video (default: 100)"
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        default="./sam2_output",
        help="Output directory for all results (default: ./sam2_output)"
    )

    # Video creation options
    parser.add_argument(
        "--create_video",
        action="store_true",
        help="Create video from processed frames"
    )

    parser.add_argument(
        "--frame_rate",
        type=int,
        default=25,
        help="Frame rate for output video (default: 25)"
    )

    # Device options
    parser.add_argument(
        "--device",
        type=str,
        choices=["cuda", "cpu", "mps"],
        help="Device to use for computation (auto-detect if not specified)"
    )

    # Interactive mode
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Enable interactive annotation mode"
    )

    # Text-based detection options
    parser.add_argument(
        "--text-prompt",
        type=str,
        help="Text prompt for automatic object detection (e.g., 'person. car. dog.'). Enables text-based detection mode."
    )

    parser.add_argument(
        "--manual-mode",
        action="store_true",
        help="Force manual annotation mode (disables text-based detection even if text-prompt is provided)"
    )

    parser.add_argument(
        "--allow-refinement",
        action="store_true",
        help="Allow manual refinement after automatic text-based detection"
    )

    # Detection parameters
    parser.add_argument(
        "--box-threshold",
        type=float,
        default=0.25,
        help="Box confidence threshold for Grounding DINO (default: 0.25)"
    )

    parser.add_argument(
        "--text-threshold",
        type=float,
        default=0.25,
        help="Text confidence threshold for Grounding DINO (default: 0.25)"
    )

    parser.add_argument(
        "--frame-step",
        type=int,
        default=20,
        help="Process every N-th frame for detection (default: 20)"
    )

    parser.add_argument(
        "--grounding-model",
        type=str,
        default="IDEA-Research/grounding-dino-tiny",
        help="HuggingFace model ID for Grounding DINO (default: IDEA-Research/grounding-dino-tiny)"
    )

    parser.add_argument(
        "--prompt-frame",
        type=int,
        default=0,
        help="Specific frame index to use for Grounded SAM 2 prompting (default: 0)"
    )

    parser.add_argument(
        "--disable-enhanced-prompts",
        action="store_true",
        help="Disable enhanced prompt engineering for Grounding DINO (use original prompts as-is)"
    )

    return parser.parse_args()


def run_frames_demo(frames_dir, model_size="base_plus", max_frames=100, output_dir=None,
                   input_size=None, text_prompt=None, allow_refinement=False, manual_mode=False,
                   interactive=False, use_enhanced_prompts=True, **kwargs):
    """
    Run SAM2 demo with frame directory input instead of video.

    Args:
        frames_dir (str): Path to directory containing video frames
        model_size (str): SAM2 model size
        max_frames (int): Maximum number of frames to process
        output_dir (str): Output directory
        input_size (int): Input size for the model
        text_prompt (str): Text prompt for automatic detection
        allow_refinement (bool): Allow manual refinement
        manual_mode (bool): Force manual mode
        interactive (bool): Interactive mode
        **kwargs: Additional arguments
    """
    try:
        frames_path = Path(frames_dir)
        if not frames_path.exists():
            raise FileNotFoundError(f"Frames directory not found: {frames_dir}")

        # Get frame files
        frame_files = sorted([f for f in frames_path.glob("*.jpg")] +
                           [f for f in frames_path.glob("*.png")])

        if not frame_files:
            raise ValueError(f"No image files found in {frames_dir}")

        print(f"Found {len(frame_files)} frames in {frames_dir}")

        # Limit frames if specified
        if max_frames and len(frame_files) > max_frames:
            frame_files = frame_files[:max_frames]
            print(f"Limited to first {max_frames} frames")

        # Create output directory structure
        if not output_dir:
            output_dir = "./sam2_output"

        dir_manager = DirectoryManager(output_dir)
        base_name = frames_path.name
        dir_manager.setup_directories(base_name)

        # Initialize predictor
        predictor = SAM2VideoPredictor(
            model_size=model_size,
            device=kwargs.get('device'),
            input_size=input_size,
            enable_grounding_dino=bool(text_prompt)
        )

        # Initialize video state with frames directory
        predictor.init_video_state(str(frames_dir))

        # Determine workflow
        if text_prompt and not manual_mode:
            # Text-based detection workflow
            detection_results = predictor.detect_objects_with_text_prompt(
                str(frames_dir),
                text_prompt,
                box_threshold=kwargs.get('box_threshold', 0.25),
                text_threshold=kwargs.get('text_threshold', 0.25),
                frame_step=kwargs.get('frame_step', 20),
                max_frames=max_frames,
                prompt_frame_idx=kwargs.get('prompt_frame_idx', 0),
                use_enhanced_prompts=use_enhanced_prompts
            )

            if allow_refinement:
                # Run interactive refinement
                run_interactive_demo_with_detections(
                    str(frames_dir), model_size, max_frames, output_dir,
                    input_size, predictor, dir_manager, detection_results, str(frames_dir)
                )
            else:
                # Direct propagation and export
                if detection_results:
                    video_segments = predictor.propagate_masks()
                    paths = dir_manager.get_paths()

                    # Save results
                    predictor.save_masks_and_json(
                        video_segments, str(frames_dir),
                        paths['mask_data_dir'], paths['json_data_dir']
                    )

                    # Save PNG mask visualization files
                    predictor.save_png_masks(video_segments, str(frames_dir), paths['mask_data_dir'])
                    predictor.export_masks(video_segments, str(frames_dir), paths['mask_data_dir'])
                    predictor.export_masks_with_overlay(video_segments, str(frames_dir), paths['result_dir'])

                    print(f"[OK] Processing completed!")
                    print(f"Results saved to: {dir_manager.base_dir}")
                else:
                    print("[ERROR] No objects detected with the given text prompt")
        else:
            # Manual/Interactive workflow
            if interactive:
                run_interactive_demo(str(frames_dir), model_size, max_frames, output_dir, input_size)
            else:
                print("Manual annotation mode not yet implemented for frame input")
                print("Use --interactive flag for interactive annotation")

    except Exception as e:
        print(f"[ERROR] Error in frames demo: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Main function for the enhanced SAM2 video predictor."""
    args = parse_arguments()

    print("=" * 80)
    print("SAM2 Video Predictor v5 - Enhanced Edition with Text-Based Detection")
    print("=" * 80)

    # Handle input source
    if args.video:
        print(f"Video: {args.video}")
        input_source = args.video
        input_type = "video"
        source_name = Path(args.video).stem
    else:
        print(f"Frames directory: {args.frames_dir}")
        input_source = args.frames_dir
        input_type = "frames"
        source_name = Path(args.frames_dir).name

    print(f"Model: {args.model}")
    print(f"Input size: {args.input_size}")
    print(f"Max frames: {args.max_frames}")
    print(f"Output directory: {args.output_dir}")
    print(f"Device: {args.device}")
    print(f"Interactive mode: {args.interactive}")

    # Determine workflow mode
    text_detection_mode = args.text_prompt and not args.manual_mode
    manual_mode = args.manual_mode or (not args.text_prompt and not args.interactive)
    hybrid_mode = text_detection_mode and args.allow_refinement

    print(f"Text prompt: {args.text_prompt if args.text_prompt else 'None'}")
    print(f"Workflow mode: {'Text-based detection' if text_detection_mode else 'Manual annotation'}")
    if hybrid_mode:
        print(f"Manual refinement: Enabled")
    print("=" * 80)

    try:
        # Handle frames directory input
        if input_type == "frames":
            run_frames_demo(
                frames_dir=input_source,
                model_size=args.model,
                max_frames=args.max_frames,
                output_dir=args.output_dir,
                input_size=args.input_size,
                text_prompt=args.text_prompt,
                allow_refinement=args.allow_refinement,
                manual_mode=args.manual_mode,
                interactive=args.interactive,
                device=args.device,
                box_threshold=args.box_threshold,
                text_threshold=args.text_threshold,
                frame_step=args.frame_step,
                use_enhanced_prompts=not args.disable_enhanced_prompts
            )
            return

        # Handle video input (original functionality)
        # Initialize directory manager
        dir_manager = DirectoryManager(args.output_dir)
        dir_manager.setup_directories(source_name)

        # Initialize SAM2 predictor with optional Grounding DINO
        predictor = SAM2VideoPredictor(
            model_size=args.model,
            device=args.device,
            input_size=args.input_size,
            enable_grounding_dino=text_detection_mode
        )

        # Print model information
        model_info = predictor.get_model_info()
        print(f"\nModel Information:")
        for key, value in model_info.items():
            print(f"  {key}: {value}")

        # Preprocess video to frames
        print(f"\nPreprocessing video...")
        frames_dir = predictor.preprocess_video(
            args.video,
            max_frames=args.max_frames,
            output_dir=str(dir_manager.base_output_dir)
        )
        dir_manager.set_video_dir(frames_dir)

        # Initialize video state
        print(f"\nInitializing video state...")
        predictor.init_video_state(frames_dir)

        # Handle different workflow modes
        if text_detection_mode:
            print(f"\n[AUTO] Starting text-based automatic detection...")

            # Run automatic detection
            detection_results = predictor.detect_objects_with_text_prompt(
                frames_dir,
                args.text_prompt,
                box_threshold=args.box_threshold,
                text_threshold=args.text_threshold,
                frame_step=args.frame_step,
                max_frames=args.max_frames,
                prompt_frame_idx=args.prompt_frame,
                use_enhanced_prompts=not args.disable_enhanced_prompts
            )

            print(f"\n[OK] Text-based detection completed!")
            print(f"Detected objects in {len([r for r in detection_results.values() if len(r.get('object_ids', [])) > 0])} frames")

            # Check if manual refinement is requested
            if hybrid_mode:
                print(f"\n[REFINE] Manual refinement mode available...")
                print("You can review and refine the detected objects before mask propagation.")
                print("This saves time by avoiding expensive computation if changes are needed.")

                # Optionally start interactive mode for refinement
                user_input = input("\nStart interactive refinement now? (y/n): ").lower().strip()
                if user_input == 'y':
                    print(f"\nStarting interactive refinement mode...")

                    # Create a modified interactive demo that includes detection results
                    run_interactive_demo_with_detections(
                        video_path=args.video,
                        model_size=args.model,
                        max_frames=args.max_frames,
                        output_dir=args.output_dir,
                        input_size=args.input_size,
                        predictor=predictor,
                        dir_manager=dir_manager,
                        detection_results=detection_results
                    )
                    return 0
                else:
                    print("Proceeding with automatic processing...")

            # Only run propagation if not in hybrid mode or user declined refinement
            print(f"\n[VIDEO] Propagating masks across video...")
            video_segments = predictor.propagate_masks()

            # Save results
            paths = dir_manager.get_paths()
            predictor.save_masks_and_json(
                video_segments,
                frames_dir,
                paths['mask_data_dir'],
                paths['json_data_dir']
            )

            # Save PNG mask visualization files
            predictor.save_png_masks(video_segments, frames_dir, paths['mask_data_dir'])

            # Export visualizations
            overlay_dir = predictor.export_masks_with_overlay(
                video_segments, frames_dir, paths['result_dir']
            )

            # Create output video
            output_video_path = str(dir_manager.base_output_dir / f"{source_name}_detected.mp4")
            create_video_from_images(overlay_dir, output_video_path, args.frame_rate)

            print(f"\n[OK] Text-based detection and processing completed!")
            print(f"Results saved to: {dir_manager.base_output_dir}")
            print(f"Video created: {output_video_path}")

        elif args.interactive:
            print(f"\n[INTERACTIVE] Starting interactive annotation mode...")
            # Run the interactive demo
            run_interactive_demo(
                video_path=args.video,
                model_size=args.model,
                max_frames=args.max_frames,
                output_dir=args.output_dir,
                input_size=args.input_size
            )
            return 0  # Exit after interactive demo
        else:
            print(f"\n[INFO] Non-interactive mode - basic processing completed.")
            print("For interactive annotation, use --interactive flag.")
            print("For text-based detection, use --text-prompt 'your prompt here'.")

        # Test video creation functionality if requested
        if args.create_video:
            print(f"\nTesting video creation functionality...")
            frames_dir = dir_manager.get_paths()['video_dir']
            if frames_dir and os.path.exists(frames_dir):
                output_video_path = str(dir_manager.base_output_dir / f"{source_name}_test.mp4")
                try:
                    create_video_from_images(frames_dir, output_video_path, args.frame_rate)
                    print(f"[OK] Video creation test successful: {output_video_path}")
                except Exception as e:
                    print(f"[ERROR] Video creation test failed: {e}")
            else:
                print("[ERROR] No frames directory found for video creation test")

        # Get directory paths for reference
        paths = dir_manager.get_paths()
        print(f"\nDirectory structure created:")
        for key, path in paths.items():
            if path:
                print(f"  {key}: {path}")

        print(f"\nProcessing completed successfully!")
        print(f"Use the interactive mode (--interactive) to add annotations and generate masks.")

    except Exception as e:
        print(f"\nError during processing: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
